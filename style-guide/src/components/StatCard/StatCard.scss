@use '../DashboardWidget/iCard/styles/variables' as *;
@use '../DashboardWidget/iCard/styles/mixins' as *;

.action-card {
  justify-content: space-between;
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 7px;
  padding: 20px 20px;
  cursor: pointer;
  flex: 1 1 200px;
  max-width: 100%;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  @include media-max(lg) {
    flex: 0 0 calc(50% - 12px);
  }
  @include media-max(sm) {
    flex: 1 1 250px;
  }
  .card-wrapper {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
    min-width: 183px;
  }
}

.action-card-content {
  flex-grow: 1;
}

.action-card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}

.action-card-details {
  display: flex;
  align-items: center;
  gap: 8px;
  line-height: 1;
}

.action-card-count {
  font-size: 23px;
  font-weight: 500;
  color: #1f4a70;
}

.action-card-description {
  font-size: 14px;
  color: #333333;
}

.action-card-icon {
  color: #1f4a70;
  margin-left: 16px;
}

/* Skeleton loader styles */
.skeleton {
  background: linear-gradient(90deg, #edf3f7 25%, #ffffff 50%, #edf3f7 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-title {
  width: 90%;
  height: 25px;
  margin-bottom: 8px;
}

.skeleton-subtitle {
  width: 30%;
  height: 25px;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
