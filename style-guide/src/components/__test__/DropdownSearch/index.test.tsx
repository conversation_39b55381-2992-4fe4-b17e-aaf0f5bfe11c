import React from "react";
import { render, fireEvent, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import { DropdownSearchControl } from "../../DropdownSearch";

describe("DropdownSearchControl", () => {
  const mockOnChange = jest.fn();
  const defaultProps = {
    options: [
      { id: "1", value: "Option 1" },
      { id: "2", value: "Option 2" },
    ],
    id: "test-dropdown",
    placeholder: "Select option",
    onChange: mockOnChange,
    value: null,
    isInvalid: false,
  };

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  it("renders with placeholder text", () => {
    render(<DropdownSearchControl {...defaultProps} />);
    expect(screen.getByTestId("test-dropdown")).toHaveAttribute(
      "placeholder",
      "Select option"
    );
  });

  it("renders in disabled state", () => {
    render(<DropdownSearchControl {...defaultProps} disabled={true} />);
    expect(screen.getByTestId("test-dropdown")).toBeDisabled();
  });

  it("renders with selected single value", () => {
    render(<DropdownSearchControl {...defaultProps} value="1" />);
    expect(screen.getByTestId("test-dropdown")).toHaveValue("Option 1");
  });

  it("calls onInputChange when input changes", () => {
    const mockOnInputChange = jest.fn();
    render(
      <DropdownSearchControl
        {...defaultProps}
        onInputChange={mockOnInputChange}
      />
    );
    const input = screen.getByTestId("test-dropdown");
    fireEvent.change(input, { target: { value: "test" } });
    expect(mockOnInputChange).toHaveBeenCalled();
  });

  it("calls onChange with null when cleared", () => {
    render(<DropdownSearchControl {...defaultProps} value="1" />);
    const input = screen.getByTestId("test-dropdown");
    fireEvent.change(input, { target: { value: "" } });
    fireEvent.blur(input);
    expect(mockOnChange).toHaveBeenCalledWith(null);
  });

  it("applies isInvalid prop correctly", () => {
    render(<DropdownSearchControl {...defaultProps} isInvalid />);
    const input = screen.getByTestId("test-dropdown");
    expect(input).toHaveClass("is-invalid");
  });

  it("applies custom className", () => {
    render(
      <DropdownSearchControl {...defaultProps} className="custom-class" />
    );
    expect(
      screen.getByTestId("test-dropdown").closest(".custom-class")
    ).toBeInTheDocument();
  });

  it("updates selected value when value prop changes", () => {
    const { rerender } = render(
      <DropdownSearchControl {...defaultProps} value="1" />
    );
    expect(screen.getByTestId("test-dropdown")).toHaveValue("Option 1");

    rerender(<DropdownSearchControl {...defaultProps} value="2" />);
    expect(screen.getByTestId("test-dropdown")).toHaveValue("Option 2");
  });
});
