import React from "react";
import { render, screen } from "@testing-library/react";
import ListWrapper from "../../DashboardWidget/iCard/ListWrapper";
import { Vessel } from "../../DashboardWidget/types/card-types";

// Mock Card Component for testing
const MockCard: React.FC<{ data: Vessel }> = ({ data }) => (
  <div data-testid="mock-card">{data.name}</div>
);

const mockResponsiveConfig = {
  designName: "test-design",
  component: MockCard,
};

const mockData: Vessel[] = [
  { id: "1", name: "Vessel A" } as Vessel,
  { id: "2", name: "Vessel B" } as Vessel,
];

describe("ListWrapper", () => {
  it("renders 10 skeleton cards when loading", () => {
    render(
      <ListWrapper
        isLoading
        data={[]}
        responsiveConfig={mockResponsiveConfig}
      />
    );
    const skeletons = screen.getAllByRole("generic", { hidden: true });
    // Check count of skeleton wrappers
    expect(
      skeletons.filter((el) => el.classList.contains("ra-card-resp")).length
    ).toBe(10);
  });

  it("renders card components when data is provided", () => {
    render(
      <ListWrapper data={mockData} responsiveConfig={mockResponsiveConfig} />
    );
    expect(screen.getByText("Vessel A")).toBeInTheDocument();
    expect(screen.getByText("Vessel B")).toBeInTheDocument();
    expect(screen.getAllByTestId("mock-card")).toHaveLength(2);
  });

  it("applies responsiveCardListContainerHeight style when provided", () => {
    const { container } = render(
      <ListWrapper
        data={[]}
        responsiveConfig={mockResponsiveConfig}
        responsiveCardListContainerHeight="500px"
      />
    );
    expect(container.firstChild).toHaveStyle({ minHeight: "500px" });
  });

  it("always applies correct container classes", () => {
    const { container } = render(
      <ListWrapper data={[]} responsiveConfig={mockResponsiveConfig} />
    );
    expect(container.firstChild).toHaveClass("ra-tableContainer");
    expect(container.firstChild).toHaveClass("table-responsive");
  });
});
