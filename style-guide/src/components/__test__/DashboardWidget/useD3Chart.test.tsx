import { renderHook } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useD3Chart } from '../../DashboardWidget/hooks/useD3Chart';

describe('useD3Chart (real implementation)', () => {
  const baseConfig = {
    vessels: [
      { name: 'V1', a: 10, b: 0, c: -5 },
      { name: 'V2', a: 20, b: 30, c: 0 },
    ],
    valueHeaders: ['a', 'b', 'c'],
    badgeColors: ['#111111', '#fbc02d', '#333333'],
    valueDomain: [0, 100] as [number, number],
    chartWidth: 200,
    heightPerBar: 20,
    margin: { top: 10, right: 10, bottom: 10, left: 10 },
  };

  test('computes chart and total height', () => {
    const { result } = renderHook(() => useD3Chart(baseConfig));
    expect(result.current.chartHeight).toBe(40); // 2 * 20
    expect(result.current.totalHeight).toBe(60); // 40 + 10 + 10
  });

  test('xScale and yScale are configured with correct ranges', () => {
    const { result } = renderHook(() => useD3Chart(baseConfig));
    const { xScale, yScale } = result.current;

    // xScale maps domain 0..100 to width (chartWidth - margins)
    expect(xScale(0)).toBeCloseTo(0, 5);
    expect(xScale(100)).toBeCloseTo(180, 5); // 200 - 10 - 10

    // yScale should return a value for each band and have non-zero bandwidth
    const y0 = yScale('0');
    const y1 = yScale('1');
    expect(typeof y0).toBe('number');
    expect(typeof y1).toBe('number');
    expect(yScale.bandwidth()).toBeGreaterThan(0);
  });

  test('color scales map headers to colors and text color handles #fbc02d as black', () => {
    const { result } = renderHook(() => useD3Chart(baseConfig));
    const { barColorScale, textColorScale } = result.current;

    expect(barColorScale('a')).toBe('#111111');
    expect(barColorScale('b')).toBe('#fbc02d');
    expect(barColorScale('c')).toBe('#333333');

    expect(textColorScale('a')).toBe('white');
    expect(textColorScale('b')).toBe('black');
    expect(textColorScale('c')).toBe('white');
  });

  test('stackedBarData builds segments, skips <= 0 values, and adds 1px gaps', () => {
    const { result } = renderHook(() => useD3Chart(baseConfig));
    const { stackedBarData, xScale, yScale } = result.current;

    expect(stackedBarData).toHaveLength(2);

    // First vessel: only 'a' is positive
    const first = stackedBarData[0];
    expect(first.segments).toHaveLength(1);
    const segA = first.segments[0];
    expect(segA.key).toBe('a');
    expect(segA.value).toBe(10);
    expect(segA.x).toBeCloseTo(0, 5);
    expect(segA.width).toBeCloseTo(xScale(10), 5);
    expect(segA.y).toBe(yScale('0'));

    // Second vessel: 'a' then 'b' with a 1px gap
    const second = stackedBarData[1];
    expect(second.segments).toHaveLength(2);
    const segA2 = second.segments[0];
    const segB2 = second.segments[1];

    expect(segA2.key).toBe('a');
    expect(segB2.key).toBe('b');

    expect(segA2.width).toBeCloseTo(xScale(20), 5);
    expect(segB2.width).toBeCloseTo(xScale(30), 5);

    // segB.x should be segA.width + 1 (gap)
    expect(segB2.x).toBeCloseTo(segA2.width + 1, 5);
  });

  test('updates memoized values when inputs change', () => {
    const { result, rerender } = renderHook(
      ({ cfg }) => useD3Chart(cfg),
      { initialProps: { cfg: baseConfig } }
    );

    const firstXScale = result.current.xScale;
    rerender({ cfg: { ...baseConfig, chartWidth: 300 } });
    const secondXScale = result.current.xScale;

    expect(secondXScale(100)).toBeCloseTo(300 - 10 - 10, 5);
    expect(firstXScale(100)).toBeCloseTo(180, 5);
  });
});
