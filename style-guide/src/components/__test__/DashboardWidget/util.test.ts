import { getSurveyActiveDueTab } from "./../../DashboardWidget/util/util";

describe("getSurveyActiveDueTab", () => {
  it("should return 'overdue' when value is 'Overdue'", () => {
    expect(getSurveyActiveDueTab("Overdue")).toBe("overdue");
  });

  it("should return 'due30' when value is 'Due within 30 Days'", () => {
    expect(getSurveyActiveDueTab("Due within 30 Days")).toBe("due30");
  });

  it("should return 'due60' when value is 'Due within 60 Days'", () => {
    expect(getSurveyActiveDueTab("Due within 60 Days")).toBe("due60");
  });

  it("should return an empty string for unknown values", () => {
    expect(getSurveyActiveDueTab("Invalid Value")).toBe("");
  });

  it("should return an empty string for null", () => {
    expect(getSurveyActiveDueTab(null)).toBe("");
  });

  it("should return an empty string for undefined", () => {
    expect(getSurveyActiveDueTab(undefined)).toBe("");
  });

  it("should return an empty string for empty string input", () => {
    expect(getSurveyActiveDueTab("")).toBe("");
  });
});
