import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { DonutChartCard } from '../../DashboardWidget/common/DonutChartCard';

// Mock Dynamic3DDonutChart component
jest.mock('../../DashboardWidget/common/Dynamic3DDonutChart', () => {
  return function MockDynamic3DDonutChart(props: any) {
    return (
      <div data-testid="dynamic-3d-donut-chart">
        <div data-testid="chart-size">{props.size}</div>
        <div data-testid="chart-data-length">{props.data.length}</div>
        {props.data.map((item: any, index: number) => (
          <div key={index} data-testid={`chart-item-${index}`}>
            <span data-testid={`label-${index}`}>{item.label}</span>
            <span data-testid={`value-${index}`}>{item.value}</span>
            <span data-testid={`color-${index}`}>{item.color}</span>
            <span data-testid={`url-${index}`}>{item.url}</span>
          </div>
        ))}
      </div>
    );
  };
});

const mockChartData = [
  {
    label: 'High Priority',
    value: 25,
    color: '#ff0000',
  },
  {
    label: 'Medium Priority',
    value: 15,
    color: '#ffff00',
  },
  {
    label: 'Low Priority',
    value: 10,
    color: '#00ff00',
  },
];

describe('DonutChartCard Component', () => {
  const defaultProps = {
    title: 'Test Chart',
    data: mockChartData,
  };

  test('renders without crashing', () => {
    render(<DonutChartCard {...defaultProps} />);
    expect(screen.getByText('Test Chart')).toBeInTheDocument();
    expect(screen.getByTestId('dynamic-3d-donut-chart')).toBeInTheDocument();
  });

  test('renders with correct CSS classes', () => {
    render(<DonutChartCard {...defaultProps} />);

    const card = screen.getByText('Test Chart').closest('.donut-chart-card');
    expect(card).toBeInTheDocument();
    expect(card).toHaveClass('donut-chart-card');

    const header = screen.getByText('Test Chart').closest('.donut-chart-header');
    expect(header).toBeInTheDocument();

    const title = screen.getByText('Test Chart');
    expect(title).toHaveClass('donut-chart-title');

    const contentWrapper = document.querySelector('.donut-chart-content-wrapper');
    expect(contentWrapper).toBeInTheDocument();

    const visual = document.querySelector('.donut-chart-visual');
    expect(visual).toBeInTheDocument();
  });

  test('displays the correct title', () => {
    render(<DonutChartCard {...defaultProps} />);
    expect(screen.getByText('Test Chart')).toBeInTheDocument();
  });

  test('displays different titles correctly', () => {
    const { rerender } = render(<DonutChartCard {...defaultProps} />);
    expect(screen.getByText('Test Chart')).toBeInTheDocument();

    rerender(<DonutChartCard {...defaultProps} title="Different Title" />);
    expect(screen.getByText('Different Title')).toBeInTheDocument();
    expect(screen.queryByText('Test Chart')).not.toBeInTheDocument();
  });

  test('passes size prop to Dynamic3DDonutChart', () => {
    render(<DonutChartCard {...defaultProps} size="90%" />);
    expect(screen.getByTestId('chart-size')).toHaveTextContent('90%');
  });

  test('passes numeric size prop to Dynamic3DDonutChart', () => {
    render(<DonutChartCard {...defaultProps} size={200} />);
    expect(screen.getByTestId('chart-size')).toHaveTextContent('200');
  });

  test('handles undefined size prop', () => {
    render(<DonutChartCard {...defaultProps} />);
    expect(screen.getByTestId('chart-size')).toHaveTextContent('');
  });

  test('formats data correctly for Dynamic3DDonutChart', () => {
    render(<DonutChartCard {...defaultProps} />);

    expect(screen.getByTestId('chart-data-length')).toHaveTextContent('3');

    // Check first item
    expect(screen.getByTestId('label-0')).toHaveTextContent('High Priority');
    expect(screen.getByTestId('value-0')).toHaveTextContent('25');
    expect(screen.getByTestId('color-0')).toHaveTextContent('#ff0000');
    expect(screen.getByTestId('url-0')).toHaveTextContent('/deficiency-details/high-priority');

    // Check second item
    expect(screen.getByTestId('label-1')).toHaveTextContent('Medium Priority');
    expect(screen.getByTestId('value-1')).toHaveTextContent('15');
    expect(screen.getByTestId('color-1')).toHaveTextContent('#ffff00');
    expect(screen.getByTestId('url-1')).toHaveTextContent('/deficiency-details/medium-priority');

    // Check third item
    expect(screen.getByTestId('label-2')).toHaveTextContent('Low Priority');
    expect(screen.getByTestId('value-2')).toHaveTextContent('10');
    expect(screen.getByTestId('color-2')).toHaveTextContent('#00ff00');
    expect(screen.getByTestId('url-2')).toHaveTextContent('/deficiency-details/low-priority');
  });

  test('handles empty data array', () => {
    const emptyDataProps = { ...defaultProps, data: [] };
    render(<DonutChartCard {...emptyDataProps} />);

    expect(screen.getByTestId('chart-data-length')).toHaveTextContent('0');
    expect(screen.getByText('Test Chart')).toBeInTheDocument();
  });

  test('handles single data item', () => {
    const singleItemData = [mockChartData[0]];
    const singleItemProps = { ...defaultProps, data: singleItemData };
    render(<DonutChartCard {...singleItemProps} />);

    expect(screen.getByTestId('chart-data-length')).toHaveTextContent('1');
    expect(screen.getByTestId('label-0')).toHaveTextContent('High Priority');
  });

  test('generates correct URLs for labels with spaces', () => {
    const spacedLabelData = [
      {
        label: 'Very High Priority Items',
        value: 30,
        color: '#red',
      },
    ];
    const spacedLabelProps = { ...defaultProps, data: spacedLabelData };
    render(<DonutChartCard {...spacedLabelProps} />);

    expect(screen.getByTestId('url-0')).toHaveTextContent('/deficiency-details/very-high-priority-items');
  });

  test('generates correct URLs for labels with special characters', () => {
    const specialCharData = [
      {
        label: 'High-Priority & Critical',
        value: 40,
        color: '#blue',
      },
    ];
    const specialCharProps = { ...defaultProps, data: specialCharData };
    render(<DonutChartCard {...specialCharProps} />);

    expect(screen.getByTestId('url-0')).toHaveTextContent('/deficiency-details/high-priority-&-critical');
  });

  test('generates correct URLs for uppercase labels', () => {
    const uppercaseData = [
      {
        label: 'URGENT ITEMS',
        value: 50,
        color: '#purple',
      },
    ];
    const uppercaseProps = { ...defaultProps, data: uppercaseData };
    render(<DonutChartCard {...uppercaseProps} />);

    expect(screen.getByTestId('url-0')).toHaveTextContent('/deficiency-details/urgent-items');
  });

  test('handles zero values in data', () => {
    const zeroValueData = [
      {
        label: 'No Items',
        value: 0,
        color: '#gray',
      },
    ];
    const zeroValueProps = { ...defaultProps, data: zeroValueData };
    render(<DonutChartCard {...zeroValueProps} />);

    expect(screen.getByTestId('value-0')).toHaveTextContent('0');
  });

  test('handles negative values in data', () => {
    const negativeValueData = [
      {
        label: 'Negative Items',
        value: -5,
        color: '#orange',
      },
    ];
    const negativeValueProps = { ...defaultProps, data: negativeValueData };
    render(<DonutChartCard {...negativeValueProps} />);

    expect(screen.getByTestId('value-0')).toHaveTextContent('-5');
  });

  test('handles large values in data', () => {
    const largeValueData = [
      {
        label: 'Many Items',
        value: 1000000,
        color: '#cyan',
      },
    ];
    const largeValueProps = { ...defaultProps, data: largeValueData };
    render(<DonutChartCard {...largeValueProps} />);

    expect(screen.getByTestId('value-0')).toHaveTextContent('1000000');
  });

  test('preserves original data properties', () => {
    render(<DonutChartCard {...defaultProps} />);

    // Verify that original label, value, and color are preserved
    expect(screen.getByTestId('label-0')).toHaveTextContent('High Priority');
    expect(screen.getByTestId('value-0')).toHaveTextContent('25');
    expect(screen.getByTestId('color-0')).toHaveTextContent('#ff0000');
  });

  test('handles multiple items with same label', () => {
    const duplicateLabelData = [
      {
        label: 'Same Label',
        value: 10,
        color: '#red',
      },
      {
        label: 'Same Label',
        value: 20,
        color: '#blue',
      },
    ];
    const duplicateLabelProps = { ...defaultProps, data: duplicateLabelData };
    render(<DonutChartCard {...duplicateLabelProps} />);

    expect(screen.getByTestId('chart-data-length')).toHaveTextContent('2');
    expect(screen.getByTestId('url-0')).toHaveTextContent('/deficiency-details/same-label');
    expect(screen.getByTestId('url-1')).toHaveTextContent('/deficiency-details/same-label');
  });

  test('handles very long titles', () => {
    const longTitle = 'This is a very long title that might wrap to multiple lines in the UI';
    render(<DonutChartCard {...defaultProps} title={longTitle} />);

    expect(screen.getByText(longTitle)).toBeInTheDocument();
  });

  test('handles empty string title', () => {
    render(<DonutChartCard {...defaultProps} title="" />);

    const titleElement = document.querySelector('.donut-chart-title');
    expect(titleElement).toBeInTheDocument();
    expect(titleElement).toHaveTextContent('');
  });

  test('handles title with special characters', () => {
    const specialTitle = 'Chart & Data Analysis (2024)';
    render(<DonutChartCard {...defaultProps} title={specialTitle} />);

    expect(screen.getByText(specialTitle)).toBeInTheDocument();
  });

  test('re-renders correctly when props change', () => {
    const { rerender } = render(<DonutChartCard {...defaultProps} />);

    expect(screen.getByText('Test Chart')).toBeInTheDocument();
    expect(screen.getByTestId('chart-data-length')).toHaveTextContent('3');

    const newData = [mockChartData[0]];
    rerender(<DonutChartCard {...defaultProps} data={newData} title="New Title" />);

    expect(screen.getByText('New Title')).toBeInTheDocument();
    expect(screen.queryByText('Test Chart')).not.toBeInTheDocument();
    expect(screen.getByTestId('chart-data-length')).toHaveTextContent('1');
  });

  test('maintains component structure', () => {
    render(<DonutChartCard {...defaultProps} />);

    // Check the component hierarchy
    const card = document.querySelector('.donut-chart-card');
    const header = card?.querySelector('.donut-chart-header');
    const contentWrapper = card?.querySelector('.donut-chart-content-wrapper');
    const visual = contentWrapper?.querySelector('.donut-chart-visual');

    expect(card).toBeInTheDocument();
    expect(header).toBeInTheDocument();
    expect(contentWrapper).toBeInTheDocument();
    expect(visual).toBeInTheDocument();
  });
});