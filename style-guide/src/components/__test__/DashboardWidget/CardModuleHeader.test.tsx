import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { CardModuleHeader } from '../../DashboardWidget/iCard/CardModuleHeader';

const mockProps = {
  title: 'Test Dashboard',
  viewMode: 'list' as const,
  isModal: false,
  IsiconRenderVisible: true,
  IsenLargeIconVisible: true,
  onViewModeChange: jest.fn(),
  onToggleModal: jest.fn(),
};

describe('CardModuleHeader Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders without crashing', () => {
    render(<CardModuleHeader {...mockProps} />);
    expect(screen.getByText('Test Dashboard')).toBeInTheDocument();
  });

  test('displays the correct title', () => {
    render(<CardModuleHeader {...mockProps} />);
    expect(screen.getByText('Test Dashboard')).toBeInTheDocument();
  });

  test('renders view mode buttons when IsiconRenderVisible is true', () => {
    render(<CardModuleHeader {...mockProps} />);

    // Should render grid and list view buttons
    expect(screen.getByLabelText('Grid view')).toBeInTheDocument();
    expect(screen.getByLabelText('List view')).toBeInTheDocument();
  });

  test('calls onViewModeChange when grid button is clicked', () => {
    render(<CardModuleHeader {...mockProps} />);

    const gridButton = screen.getByLabelText('Grid view');
    fireEvent.click(gridButton);

    expect(mockProps.onViewModeChange).toHaveBeenCalledWith('grid');
  });

  test('calls onViewModeChange when list button is clicked', () => {
    render(<CardModuleHeader {...mockProps} />);

    const listButton = screen.getByLabelText('List view');
    fireEvent.click(listButton);

    expect(mockProps.onViewModeChange).toHaveBeenCalledWith('list');
  });

  test('renders enlarge icon when IsenLargeIconVisible is true and not modal', () => {
    render(<CardModuleHeader {...mockProps} />);

    expect(screen.getByLabelText('Enlarge view')).toBeInTheDocument();
  });

  test('renders minimize icon when isModal is true', () => {
    render(<CardModuleHeader {...mockProps} isModal={true} />);

    expect(screen.getByLabelText('Minimize view')).toBeInTheDocument();
  });

  test('calls onToggleModal when enlarge icon is clicked', () => {
    render(<CardModuleHeader {...mockProps} />);

    const enlargeIcon = screen.getByLabelText('Enlarge view');
    fireEvent.click(enlargeIcon);

    expect(mockProps.onToggleModal).toHaveBeenCalled();
  });

  test('calls onToggleModal when minimize icon is clicked', () => {
    render(<CardModuleHeader {...mockProps} isModal={true} />);

    const minimizeIcon = screen.getByLabelText('Minimize view');
    fireEvent.click(minimizeIcon);

    expect(mockProps.onToggleModal).toHaveBeenCalled();
  });

  test('applies correct CSS classes', () => {
    const { container } = render(<CardModuleHeader {...mockProps} />);

    const headerElement = container.querySelector('.ra-vessel-module-header');
    expect(headerElement).toBeInTheDocument();

    const titleElement = container.querySelector('.ra-vessel-module-title');
    expect(titleElement).toBeInTheDocument();

    const controlsElement = container.querySelector('.ra-vessel-module-controls');
    expect(controlsElement).toBeInTheDocument();
  });

  test('renders with minimal visibility config', () => {
    const minimalProps = {
      ...mockProps,
      IsiconRenderVisible: false,
      IsenLargeIconVisible: false,
    };

    render(<CardModuleHeader {...minimalProps} />);

    expect(screen.getByText('Test Dashboard')).toBeInTheDocument();
    expect(screen.queryByLabelText('Grid view')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('List view')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('Enlarge view')).not.toBeInTheDocument();
  });

  test('highlights active view mode correctly', () => {
    render(<CardModuleHeader {...mockProps} viewMode="list" />);

    const listButton = screen.getByLabelText('List view');
    const gridButton = screen.getByLabelText('Grid view');

    expect(listButton).toHaveClass('active');
    expect(gridButton).not.toHaveClass('active');
  });

  test('highlights grid view mode correctly', () => {
    render(<CardModuleHeader {...mockProps} viewMode="grid" />);

    const listButton = screen.getByLabelText('List view');
    const gridButton = screen.getByLabelText('Grid view');

    expect(gridButton).toHaveClass('active');
    expect(listButton).not.toHaveClass('active');
  });

  test('renders without title', () => {
    const propsWithoutTitle = { ...mockProps, title: '' };
    render(<CardModuleHeader {...propsWithoutTitle} />);

    const { container } = render(<CardModuleHeader {...propsWithoutTitle} />);
    expect(container.querySelector('.ra-vessel-module-header')).toBeInTheDocument();
  });

  test('handles missing callbacks gracefully', () => {
    const propsWithoutCallbacks = {
      ...mockProps,
      onViewModeChange: jest.fn(), // Keep as function to avoid runtime errors
      onToggleModal: jest.fn(),
    };

    // Should render without crashing
    expect(() => {
      render(<CardModuleHeader {...propsWithoutCallbacks} />);
    }).not.toThrow();
  });
});
