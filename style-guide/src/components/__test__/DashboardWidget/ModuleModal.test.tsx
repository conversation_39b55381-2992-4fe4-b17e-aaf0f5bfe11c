import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ModuleModal } from '../../DashboardWidget/iCard/ModuleModal';

const mockProps = {
  isOpen: true,
  onClose: jest.fn(),
  sizeKey: 'md' as const,
  children: <div data-testid="modal-content">Test Modal Content</div>,
};

describe('ModuleModal Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders when isOpen is true', () => {
    render(<ModuleModal {...mockProps} />);
    expect(screen.getByTestId('modal-content')).toBeInTheDocument();
  });

  test('does not render when isOpen is false', () => {
    render(<ModuleModal {...mockProps} isOpen={false} />);
    expect(screen.queryByTestId('modal-content')).not.toBeInTheDocument();
  });

  test('renders children content', () => {
    render(<ModuleModal {...mockProps} />);
    expect(screen.getByTestId('modal-content')).toBeInTheDocument();
    expect(screen.getByText('Test Modal Content')).toBeInTheDocument();
  });

  test('calls onClose when overlay is clicked', () => {
    const { container } = render(<ModuleModal {...mockProps} />);

    const overlay = container.querySelector('.ra-vessel-card-modal-overlay');
    fireEvent.click(overlay!);

    expect(mockProps.onClose).toHaveBeenCalledTimes(1);
  });

  test('does not call onClose when modal content is clicked', () => {
    const { container } = render(<ModuleModal {...mockProps} />);

    const modalContent = container.querySelector('.ra-vessel-card-modal');
    fireEvent.click(modalContent!);

    expect(mockProps.onClose).not.toHaveBeenCalled();
  });

  test('stops propagation when modal content is clicked', () => {
    const { container } = render(<ModuleModal {...mockProps} />);

    const modalContent = container.querySelector('.ra-vessel-card-modal');
    const stopPropagationSpy = jest.fn();

    const event = new MouseEvent('click', { bubbles: true });
    Object.defineProperty(event, 'stopPropagation', {
      value: stopPropagationSpy,
    });

    modalContent!.dispatchEvent(event);
    expect(mockProps.onClose).not.toHaveBeenCalled();
  });

  test('applies correct CSS classes', () => {
    const { container } = render(<ModuleModal {...mockProps} />);

    const overlay = container.querySelector('.ra-vessel-card-modal-overlay');
    expect(overlay).toBeInTheDocument();

    const modal = container.querySelector('.ra-vessel-card-modal');
    expect(modal).toBeInTheDocument();
  });

  test('applies size class correctly', () => {
    const { container } = render(<ModuleModal {...mockProps} sizeKey="lg" />);

    const modal = container.querySelector('.ra-vessel-card-modal');
    expect(modal).toHaveClass('size-lg');
  });

  test('applies different size classes', () => {
    const { container, rerender } = render(<ModuleModal {...mockProps} sizeKey="sm" />);

    let modal = container.querySelector('.ra-vessel-card-modal');
    expect(modal).toHaveClass('size-sm');

    rerender(<ModuleModal {...mockProps} sizeKey="md" />);
    modal = container.querySelector('.ra-vessel-card-modal');
    expect(modal).toHaveClass('size-md');

    rerender(<ModuleModal {...mockProps} sizeKey="lg" />);
    modal = container.querySelector('.ra-vessel-card-modal');
    expect(modal).toHaveClass('size-lg');
  });

  test('renders with complex children', () => {
    const complexChildren = (
      <div>
        <h2>Modal Title</h2>
        <p>Modal description</p>
        <button>Action Button</button>
      </div>
    );
    
    render(<ModuleModal {...mockProps} children={complexChildren} />);
    
    expect(screen.getByText('Modal Title')).toBeInTheDocument();
    expect(screen.getByText('Modal description')).toBeInTheDocument();
    expect(screen.getByText('Action Button')).toBeInTheDocument();
  });

  test('renders with empty children', () => {
    const { container } = render(<ModuleModal {...mockProps} children={null} />);

    // Should render modal structure even with no children
    expect(container.querySelector('.ra-vessel-card-modal')).toBeInTheDocument();
  });

  test('handles rapid open/close cycles', () => {
    const { rerender } = render(<ModuleModal {...mockProps} isOpen={false} />);

    // Should not render when closed
    expect(screen.queryByTestId('modal-content')).not.toBeInTheDocument();

    // Rapidly toggle modal
    rerender(<ModuleModal {...mockProps} isOpen={true} />);
    expect(screen.getByTestId('modal-content')).toBeInTheDocument();

    rerender(<ModuleModal {...mockProps} isOpen={false} />);
    expect(screen.queryByTestId('modal-content')).not.toBeInTheDocument();

    rerender(<ModuleModal {...mockProps} isOpen={true} />);
    expect(screen.getByTestId('modal-content')).toBeInTheDocument();
  });

  test('handles overlay click correctly with event target check', () => {
    const { container } = render(<ModuleModal {...mockProps} />);

    const overlay = container.querySelector('.ra-vessel-card-modal-overlay');
    const modal = container.querySelector('.ra-vessel-card-modal');

    // Click on overlay should close
    fireEvent.click(overlay!);
    expect(mockProps.onClose).toHaveBeenCalledTimes(1);

    // Reset mock
    mockProps.onClose.mockClear();

    // Click on modal content should not close (event propagation stopped)
    fireEvent.click(modal!);
    expect(mockProps.onClose).not.toHaveBeenCalled();
  });
});
