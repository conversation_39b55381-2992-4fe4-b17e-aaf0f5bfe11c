import React from "react";
import { render, fireEvent, screen } from "@testing-library/react";
import { CardDropdownMenu } from "../../DashboardWidget/iCard/CardDropdownMenu";

describe("CardDropdownMenu", () => {
  const mockProps = {
    filteredGroups: [
      {
        id: 1,
        title: "Group 1",
        vessels: [
          { vessel_id: 1, name: "Vessel 1", vessel_account_code_new: "V001" },
          { vessel_id: 2, name: "Vessel 2", vessel_account_code_new: "V002" }
        ],
      },
    ],
    selectedItems: ["Vessel 1"],
    onToggleVessel: jest.fn(),
    onToggleGroup: jest.fn(),
    isSearchBoxVisible: true,
    searchTerm: "",
    onSearchChange: jest.fn(),
    isSelectAllVisible: true,
    isAllSelected: false,
    onToggleAll: jest.fn(),
  };

  it("renders search input when isSearchBoxVisible is true", () => {
    render(<CardDropdownMenu {...mockProps} />);
    expect(screen.getByPlaceholderText("Search")).toBeInTheDocument();
  });

  it("does not render search input when isSearchBoxVisible is false", () => {
    render(<CardDropdownMenu {...mockProps} isSearchBoxVisible={false} />);
    expect(screen.queryByPlaceholderText("Search")).not.toBeInTheDocument();
  });

  it("handles search input changes", () => {
    render(<CardDropdownMenu {...mockProps} />);
    fireEvent.change(screen.getByPlaceholderText("Search"), {
      target: { value: "test" },
    });
    expect(mockProps.onSearchChange).toHaveBeenCalledWith("test");
  });

  it("renders vessel list with correct selected states", () => {
    render(<CardDropdownMenu {...mockProps} />);
    expect(screen.getByText("Vessel 1")).toBeInTheDocument();
    expect(screen.getByText("Vessel 2")).toBeInTheDocument();
  });

  it("handles vessel selection", () => {
    render(<CardDropdownMenu {...mockProps} />);
    fireEvent.click(screen.getByText("Vessel 2"));
    expect(mockProps.onToggleVessel).toHaveBeenCalledWith("Vessel 2");
  });

  it("handles keyboard navigation for vessel selection", () => {
    render(<CardDropdownMenu {...mockProps} />);
    const vessel = screen.getByText("Vessel 2");
    fireEvent.keyDown(vessel, { key: "Enter" });
    expect(mockProps.onToggleVessel).toHaveBeenCalledWith("Vessel 2");
  });

  it("renders select all footer when isSelectAllVisible is true", () => {
    render(<CardDropdownMenu {...mockProps} />);
    expect(screen.getByText("Select All")).toBeInTheDocument();
  });

  it("does not render select all footer when isSelectAllVisible is false", () => {
    render(<CardDropdownMenu {...mockProps} isSelectAllVisible={false} />);
    expect(screen.queryByText("Select All")).not.toBeInTheDocument();
  });

  it("handles select all toggle", () => {
    render(<CardDropdownMenu {...mockProps} />);
    fireEvent.click(screen.getByText("Select All"));
    expect(mockProps.onToggleAll).toHaveBeenCalled();
  });
});
