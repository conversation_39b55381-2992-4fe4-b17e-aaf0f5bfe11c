import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import Dynamic3DDonutChart from '../../DashboardWidget/common/Dynamic3DDonutChart';

// Mock Highcharts and HighchartsReact
jest.mock('highcharts', () => ({
  __esModule: true,
  default: {
    chart: jest.fn(),
    Color: jest.fn(),
  },
}));

// Mock Highcharts 3D module
jest.mock('highcharts/highcharts-3d', () => ({}));

jest.mock('highcharts-react-official', () => {
  return function MockHighchartsReact(props: any) {
    return (
      <div data-testid="highcharts-react" className={props.containerProps?.className}>
        <div data-testid="chart-type">{props.options?.chart?.type}</div>
        <div data-testid="chart-background">{props.options?.chart?.backgroundColor}</div>
        <div data-testid="chart-3d-enabled">{props.options?.chart?.options3d?.enabled?.toString()}</div>
        <div data-testid="chart-series-length">{props.options?.series?.length}</div>
        <div data-testid="chart-data-length">{props.options?.series?.[0]?.data?.length}</div>
        {props.options?.series?.[0]?.data?.map((item: any, index: number) => (
          <div key={index} data-testid={`chart-data-${index}`}>
            <span data-testid={`name-${index}`}>{item.name}</span>
            <span data-testid={`y-${index}`}>{item.y}</span>
            <span data-testid={`color-${index}`}>{item.color}</span>
            <span data-testid={`url-${index}`}>{item.url}</span>
          </div>
        ))}
      </div>
    );
  };
});

// Mock the useHighchartsDonut hook
const mockOptions = {
  chart: {
    type: 'pie',
    backgroundColor: 'transparent',
    options3d: { enabled: true, alpha: 45, beta: 0 },
  },
  series: [{ type: 'pie', name: 'Count', data: [] }],
};

const mockTooltipStyle = '.custom-tooltip { color: red; }';

jest.mock('../../DashboardWidget/hooks/useHighchartsDonut', () => ({
  useHighchartsDonut: jest.fn(() => ({
    options: mockOptions,
    tooltipStyle: mockTooltipStyle,
  })),
}));

// Mock CSS import
jest.mock('../../DashboardWidget/common/styles/DonutChartCard.scss', () => ({}));

const mockChartData = [
  {
    label: 'Category A',
    value: 30,
    color: '#ff0000',
    url: '/category-a',
  },
  {
    label: 'Category B',
    value: 20,
    color: '#00ff00',
    url: '/category-b',
  },
  {
    label: 'Category C',
    value: 50,
    color: '#0000ff',
    url: '/category-c',
  },
];

describe('Dynamic3DDonutChart Component', () => {
  const { useHighchartsDonut } = require('../../DashboardWidget/hooks/useHighchartsDonut');

  beforeEach(() => {
    jest.clearAllMocks();

    // Reset mock implementation
    useHighchartsDonut.mockReturnValue({
      options: {
        ...mockOptions,
        series: [{
          type: 'pie',
          name: 'Count',
          data: mockChartData.map(item => ({
            name: item.label,
            y: item.value,
            color: item.color,
            url: item.url,
          })),
        }],
      },
      tooltipStyle: mockTooltipStyle,
    });
  });

  test('renders without crashing', () => {
    render(<Dynamic3DDonutChart data={mockChartData} />);
    expect(screen.getByTestId('highcharts-react')).toBeInTheDocument();
  });

  test('renders with correct container structure', () => {
    render(<Dynamic3DDonutChart data={mockChartData} />);

    const container = screen.getByTestId('highcharts-react').parentElement;
    expect(container).toHaveClass('donut-chart-container');
  });

  test('applies correct CSS class to HighchartsReact container', () => {
    render(<Dynamic3DDonutChart data={mockChartData} />);

    const highchartsContainer = screen.getByTestId('highcharts-react');
    expect(highchartsContainer).toHaveClass('donut-chart-highchart');
  });

  test('injects tooltip styles', () => {
    render(<Dynamic3DDonutChart data={mockChartData} />);

    const styleElement = document.querySelector('style');
    expect(styleElement).toBeInTheDocument();
    expect(styleElement?.textContent).toBe(mockTooltipStyle);
  });

  test('calls useHighchartsDonut hook with correct parameters', () => {
    render(<Dynamic3DDonutChart data={mockChartData} size="80%" />);

    expect(useHighchartsDonut).toHaveBeenCalledWith({
      data: mockChartData,
      size: '80%',
    });
  });

  test('calls useHighchartsDonut hook with numeric size', () => {
    render(<Dynamic3DDonutChart data={mockChartData} size={200} />);

    expect(useHighchartsDonut).toHaveBeenCalledWith({
      data: mockChartData,
      size: 200,
    });
  });

  test('calls useHighchartsDonut hook without size parameter', () => {
    render(<Dynamic3DDonutChart data={mockChartData} />);

    expect(useHighchartsDonut).toHaveBeenCalledWith({
      data: mockChartData,
      size: undefined,
    });
  });

  test('passes correct chart options to HighchartsReact', () => {
    render(<Dynamic3DDonutChart data={mockChartData} />);

    expect(screen.getByTestId('chart-type')).toHaveTextContent('pie');
    expect(screen.getByTestId('chart-background')).toHaveTextContent('transparent');
    expect(screen.getByTestId('chart-3d-enabled')).toHaveTextContent('true');
    expect(screen.getByTestId('chart-series-length')).toHaveTextContent('1');
    expect(screen.getByTestId('chart-data-length')).toHaveTextContent('3');
  });

  test('transforms data correctly for Highcharts', () => {
    render(<Dynamic3DDonutChart data={mockChartData} />);

    // Check first data item
    expect(screen.getByTestId('name-0')).toHaveTextContent('Category A');
    expect(screen.getByTestId('y-0')).toHaveTextContent('30');
    expect(screen.getByTestId('color-0')).toHaveTextContent('#ff0000');
    expect(screen.getByTestId('url-0')).toHaveTextContent('/category-a');

    // Check second data item
    expect(screen.getByTestId('name-1')).toHaveTextContent('Category B');
    expect(screen.getByTestId('y-1')).toHaveTextContent('20');
    expect(screen.getByTestId('color-1')).toHaveTextContent('#00ff00');
    expect(screen.getByTestId('url-1')).toHaveTextContent('/category-b');

    // Check third data item
    expect(screen.getByTestId('name-2')).toHaveTextContent('Category C');
    expect(screen.getByTestId('y-2')).toHaveTextContent('50');
    expect(screen.getByTestId('color-2')).toHaveTextContent('#0000ff');
    expect(screen.getByTestId('url-2')).toHaveTextContent('/category-c');
  });

  test('handles empty data array', () => {
    useHighchartsDonut.mockReturnValue({
      options: {
        ...mockOptions,
        series: [{ type: 'pie', name: 'Count', data: [] }],
      },
      tooltipStyle: mockTooltipStyle,
    });

    render(<Dynamic3DDonutChart data={[]} />);

    expect(screen.getByTestId('chart-data-length')).toHaveTextContent('0');
    expect(useHighchartsDonut).toHaveBeenCalledWith({
      data: [],
      size: undefined,
    });
  });

  test('handles single data item', () => {
    const singleItem = [mockChartData[0]];

    useHighchartsDonut.mockReturnValue({
      options: {
        ...mockOptions,
        series: [{
          type: 'pie',
          name: 'Count',
          data: [{
            name: singleItem[0].label,
            y: singleItem[0].value,
            color: singleItem[0].color,
            url: singleItem[0].url,
          }],
        }],
      },
      tooltipStyle: mockTooltipStyle,
    });

    render(<Dynamic3DDonutChart data={singleItem} />);

    expect(screen.getByTestId('chart-data-length')).toHaveTextContent('1');
    expect(screen.getByTestId('name-0')).toHaveTextContent('Category A');
  });

  test('handles data with zero values', () => {
    const zeroValueData = [
      {
        label: 'Zero Category',
        value: 0,
        color: '#gray',
        url: '/zero',
      },
    ];

    useHighchartsDonut.mockReturnValue({
      options: {
        ...mockOptions,
        series: [{
          type: 'pie',
          name: 'Count',
          data: [{
            name: 'Zero Category',
            y: 0,
            color: '#gray',
            url: '/zero',
          }],
        }],
      },
      tooltipStyle: mockTooltipStyle,
    });

    render(<Dynamic3DDonutChart data={zeroValueData} />);

    expect(screen.getByTestId('y-0')).toHaveTextContent('0');
  });

  test('handles data with negative values', () => {
    const negativeValueData = [
      {
        label: 'Negative Category',
        value: -10,
        color: '#red',
        url: '/negative',
      },
    ];

    useHighchartsDonut.mockReturnValue({
      options: {
        ...mockOptions,
        series: [{
          type: 'pie',
          name: 'Count',
          data: [{
            name: 'Negative Category',
            y: -10,
            color: '#red',
            url: '/negative',
          }],
        }],
      },
      tooltipStyle: mockTooltipStyle,
    });

    render(<Dynamic3DDonutChart data={negativeValueData} />);

    expect(screen.getByTestId('y-0')).toHaveTextContent('-10');
  });

  test('handles data with large values', () => {
    const largeValueData = [
      {
        label: 'Large Category',
        value: 1000000,
        color: '#blue',
        url: '/large',
      },
    ];

    useHighchartsDonut.mockReturnValue({
      options: {
        ...mockOptions,
        series: [{
          type: 'pie',
          name: 'Count',
          data: [{
            name: 'Large Category',
            y: 1000000,
            color: '#blue',
            url: '/large',
          }],
        }],
      },
      tooltipStyle: mockTooltipStyle,
    });

    render(<Dynamic3DDonutChart data={largeValueData} />);

    expect(screen.getByTestId('y-0')).toHaveTextContent('1000000');
  });

  test('re-renders when data changes', () => {
    const { rerender } = render(<Dynamic3DDonutChart data={mockChartData} />);

    expect(useHighchartsDonut).toHaveBeenCalledTimes(1);
    expect(screen.getByTestId('chart-data-length')).toHaveTextContent('3');

    const newData = [mockChartData[0]];
    useHighchartsDonut.mockReturnValue({
      options: {
        ...mockOptions,
        series: [{
          type: 'pie',
          name: 'Count',
          data: [{
            name: newData[0].label,
            y: newData[0].value,
            color: newData[0].color,
            url: newData[0].url,
          }],
        }],
      },
      tooltipStyle: mockTooltipStyle,
    });

    rerender(<Dynamic3DDonutChart data={newData} />);

    expect(useHighchartsDonut).toHaveBeenCalledTimes(2);
    expect(screen.getByTestId('chart-data-length')).toHaveTextContent('1');
  });

  test('re-renders when size changes', () => {
    const { rerender } = render(<Dynamic3DDonutChart data={mockChartData} size="70%" />);

    expect(useHighchartsDonut).toHaveBeenCalledWith({
      data: mockChartData,
      size: '70%',
    });

    rerender(<Dynamic3DDonutChart data={mockChartData} size="90%" />);

    expect(useHighchartsDonut).toHaveBeenCalledWith({
      data: mockChartData,
      size: '90%',
    });
  });

  test('handles complex data structures', () => {
    const complexData = [
      {
        label: 'Complex Category With Spaces & Special Characters!',
        value: 42.5,
        color: 'rgba(255, 0, 0, 0.8)',
        url: '/complex-category-with-spaces-special-characters',
      },
    ];

    useHighchartsDonut.mockReturnValue({
      options: {
        ...mockOptions,
        series: [{
          type: 'pie',
          name: 'Count',
          data: [{
            name: complexData[0].label,
            y: complexData[0].value,
            color: complexData[0].color,
            url: complexData[0].url,
          }],
        }],
      },
      tooltipStyle: mockTooltipStyle,
    });

    render(<Dynamic3DDonutChart data={complexData} />);

    expect(screen.getByTestId('name-0')).toHaveTextContent('Complex Category With Spaces & Special Characters!');
    expect(screen.getByTestId('y-0')).toHaveTextContent('42.5');
    expect(screen.getByTestId('color-0')).toHaveTextContent('rgba(255, 0, 0, 0.8)');
  });

  test('maintains component structure across re-renders', () => {
    const { rerender } = render(<Dynamic3DDonutChart data={mockChartData} />);

    expect(document.querySelector('.donut-chart-container')).toBeInTheDocument();
    expect(document.querySelector('style')).toBeInTheDocument();
    expect(screen.getByTestId('highcharts-react')).toBeInTheDocument();

    rerender(<Dynamic3DDonutChart data={[]} />);

    expect(document.querySelector('.donut-chart-container')).toBeInTheDocument();
    expect(document.querySelector('style')).toBeInTheDocument();
    expect(screen.getByTestId('highcharts-react')).toBeInTheDocument();
  });
});