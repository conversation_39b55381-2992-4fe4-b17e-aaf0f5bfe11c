import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import CardList from "../../DashboardWidget/iCard/CardList";
import { ColumnDef } from "@tanstack/react-table";
import { Vessel } from "../../DashboardWidget/types/card-types";

// Base columns for table rendering
const createColumns = (): ColumnDef<Vessel>[] => [
  {
    id: "name",
    accessorKey: "name",
    header: "Name",
    cell: ({ getValue }) => <span>{getValue() as string}</span>,
  },
  {
    id: "value",
    // accessorKey is a free string in ColumnDef, but not part of Vessel type; valid for runtime sorting
    accessorKey: "value" as any,
    header: "Value",
    cell: ({ getValue }) => <span>{String(getValue() as unknown)}</span>,
  },
];

// Mock responsive card component for ListWrapper usage
const MockResponsiveCard: React.FC<{ data: Vessel }> = ({ data }) => (
  <div data-testid="mock-responsive-card">{data.name}</div>
);

const responsiveConfig = {
  designName: "mock-design",
  component: MockResponsiveCard,
};

// Sample vessels (with extra runtime field `value` used only for sorting)
const makeVessels = () => [
  { id: "1", name: "Vessel A", value: 300 } as any as Vessel,
  { id: "2", name: "Vessel B", value: 100 } as any as Vessel,
  { id: "3", name: "Vessel C", value: 200 } as any as Vessel,
];

const pagination = { totalItems: 3, totalPages: 1, page: 1, pageSize: 10 } as any;

describe("CardList", () => {
  test("renders table view when not in responsive mobile mode", () => {
    const vessels = makeVessels();
    render(
      <CardList
        vessels={vessels}
        columns={createColumns()}
        pagination={pagination}
        isFetchingNextPage={false}
        isLoading={false}
        fetchNextPage={jest.fn()}
        responsive={false}
        responsiveConfig={responsiveConfig}
        isMobileOrTablet={false}
      />
    );

    expect(screen.getByRole("table")).toBeInTheDocument();
    // container class applied
    const container = document.querySelector(".cardTableContainer");
    expect(container).toBeInTheDocument();
  });

  test("renders ListWrapper when responsive and on mobile/tablet", () => {
    const vessels = makeVessels();
    render(
      <CardList
        vessels={vessels}
        columns={createColumns()}
        pagination={pagination}
        isFetchingNextPage={false}
        isLoading={false}
        fetchNextPage={jest.fn()}
        responsive={true}
        responsiveConfig={responsiveConfig}
        isMobileOrTablet={true}
      />
    );

    // Should not render table
    expect(screen.queryByRole("table")).not.toBeInTheDocument();
    // Should render our mock responsive cards
    expect(screen.getAllByTestId("mock-responsive-card")).toHaveLength(3);
  });

  test("clicking column header updates sorting and reorders rows via parent CardList", () => {
    const vessels = makeVessels();
    render(
      <CardList
        vessels={vessels}
        columns={createColumns()}
        pagination={pagination}
        isFetchingNextPage={false}
        isLoading={false}
        fetchNextPage={jest.fn()}
        responsive={false}
        responsiveConfig={responsiveConfig}
        isMobileOrTablet={false}
      />
    );

    const getOrder = () => screen.getAllByText(/Vessel [ABC]/).map((el) => el.textContent);
    const initialOrder = getOrder();
    expect(initialOrder).toEqual(["Vessel A", "Vessel B", "Vessel C"]);

    // Click on Value header to sort
    fireEvent.click(screen.getByText("Value"));

    const firstOrder = getOrder();
    const asc = ["Vessel B", "Vessel C", "Vessel A"]; // 100,200,300
    const desc = ["Vessel A", "Vessel C", "Vessel B"]; // 300,200,100

    // Should be either ascending or descending depending on library default
    const isAsc = firstOrder.join("|") === asc.join("|");
    const isDesc = firstOrder.join("|") === desc.join("|");
    expect(isAsc || isDesc).toBe(true);

    // Clicking again should toggle to the other order
    fireEvent.click(screen.getByText("Value"));
    const secondOrder = getOrder();
    if (isAsc) {
      expect(secondOrder).toEqual(desc);
    } else {
      expect(secondOrder).toEqual(asc);
    }
  });

  test("does not sort when column has enableSorting=false", () => {
    const vessels = makeVessels();
    const noSortColumns: ColumnDef<Vessel>[] = [
      { id: "name", accessorKey: "name", header: "Name", enableSorting: false },
      { id: "value", accessorKey: "value" as any, header: "Value", enableSorting: false },
    ];

    render(
      <CardList
        vessels={vessels}
        columns={noSortColumns}
        pagination={pagination}
        isFetchingNextPage={false}
        isLoading={false}
        fetchNextPage={jest.fn()}
        responsive={false}
        responsiveConfig={responsiveConfig}
        isMobileOrTablet={false}
      />
    );

    const before = screen.getAllByText(/Vessel [ABC]/).map((el) => el.textContent);
    fireEvent.click(screen.getByText("Value"));
    const after = screen.getAllByText(/Vessel [ABC]/).map((el) => el.textContent);
    expect(after).toEqual(before); // unchanged
  });

  test("onVesselClick is passed down and triggers when clicking vessel cell", () => {
    const vessels = makeVessels();
    const onVesselClick = jest.fn();
    // Ensure first column id is 'vessel' to trigger click handler inside InfiniteScrollTable
    const columns: ColumnDef<Vessel>[] = [
      { id: "vessel", accessorKey: "name", header: "Name" } as any,
      { id: "value", accessorKey: "value" as any, header: "Value" },
    ];

    render(
      <CardList
        vessels={vessels}
        columns={columns}
        pagination={pagination}
        isFetchingNextPage={false}
        isLoading={false}
        fetchNextPage={jest.fn()}
        onVesselClick={onVesselClick}
        responsive={false}
        responsiveConfig={responsiveConfig}
        isMobileOrTablet={false}
      />
    );

    fireEvent.click(screen.getByText("Vessel A"));
    expect(onVesselClick).toHaveBeenCalledWith(
      expect.objectContaining({ id: "1", name: "Vessel A" })
    );
  });

  test("passes isLoading to ListWrapper in responsive mode (skeletons rendered)", () => {
    render(
      <CardList
        vessels={[]}
        columns={createColumns()}
        pagination={pagination}
        isFetchingNextPage={false}
        isLoading={true}
        fetchNextPage={jest.fn()}
        responsive={true}
        responsiveConfig={responsiveConfig}
        isMobileOrTablet={true}
      />
    );

    // Expect skeleton wrappers rendered by ListWrapper (10 by default)
    const skeletonContainers = Array.from(
      document.querySelectorAll(".ra-card-resp")
    );
    expect(skeletonContainers.length).toBe(10);
  });
});
