import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import { CardDropdownSelectGroup } from "../../DashboardWidget/iCard/CardSelectGroup";
import { VesselGroup } from "../../DashboardWidget/types/card-types";

// Mock the CardDropdown component
jest.mock("../../DashboardWidget/iCard/CardDropdown", () => ({
  CardDropdown: function MockCardDropdown(props: any) {
    return (
      <div data-testid="card-dropdown">
        <div data-testid="dropdown-placeholder">{props.placeholder}</div>
        <div data-testid="dropdown-width">{props.width}</div>
        <div data-testid="selected-items">{props.selectedItems.join(",")}</div>
        <div data-testid="search-visible">
          {props.isSearchBoxVisible?.toString()}
        </div>
        <div data-testid="select-all-visible">
          {props.isSelectAllVisible?.toString()}
        </div>
        <button
          data-testid="trigger-selection-change"
          onClick={() => props.onSelectionChange(["vessel1", "vessel2"])}
        >
          Change Selection
        </button>
      </div>
    );
  },
}));

// Mock classNames
jest.mock("classnames", () => {
  return function classNames(...args: any[]) {
    return args.filter(Boolean).join(" ");
  };
});

const mockGroups: VesselGroup[] = [
  {
    id: 1,
    title: "Group 1",
    vessels: [
      { vessel_id: 1, name: "Vessel 1", vessel_account_code_new: "V001" },
      { vessel_id: 2, name: "Vessel 2", vessel_account_code_new: "V002" },
    ],
  },
  {
    id: 2,
    title: "Group 2",
    vessels: [
      { vessel_id: 3, name: "Vessel 3", vessel_account_code_new: "V003" },
      { vessel_id: 4, name: "Vessel 4", vessel_account_code_new: "V004" },
    ],
  },
];

const defaultProps = {
  index: 0,
  config: {
    placeholder: "Select Vessels",
    width: "200px",
  },
  selectedItems: ["Vessel 1"],
  groups: mockGroups,
  onChange: jest.fn(),
  isSearchBoxVisible: true,
  isSelectAllVisible: true,
};

describe("CardDropdownSelectGroup Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Clear console.error mock
    jest.spyOn(console, "error").mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("Rendering", () => {
    test("should render CardDropdown with correct props", () => {
      render(<CardDropdownSelectGroup {...defaultProps} />);

      expect(screen.getByTestId("card-dropdown")).toBeInTheDocument();
      expect(screen.getByTestId("dropdown-placeholder")).toHaveTextContent(
        "Select Vessels"
      );
      expect(screen.getByTestId("dropdown-width")).toHaveTextContent("200px");
      expect(screen.getByTestId("selected-items")).toHaveTextContent(
        "Vessel 1"
      );
      expect(screen.getByTestId("search-visible")).toHaveTextContent("true");
      expect(screen.getByTestId("select-all-visible")).toHaveTextContent(
        "true"
      );
    });

    test("should render with default width when not provided", () => {
      const propsWithoutWidth = {
        ...defaultProps,
        config: { placeholder: "Select Vessels" },
      };

      render(<CardDropdownSelectGroup {...propsWithoutWidth} />);

      expect(screen.getByTestId("dropdown-width")).toHaveTextContent("200px");
    });

    test("should render with multiple selected items", () => {
      const multiSelectProps = {
        ...defaultProps,
        selectedItems: ["Vessel 1", "Vessel 2", "Vessel 3"],
      };

      render(<CardDropdownSelectGroup {...multiSelectProps} />);

      expect(screen.getByTestId("selected-items")).toHaveTextContent(
        "Vessel 1,Vessel 2,Vessel 3"
      );
    });

    test("should render with empty selection", () => {
      const emptySelectProps = {
        ...defaultProps,
        selectedItems: [],
      };

      render(<CardDropdownSelectGroup {...emptySelectProps} />);

      expect(screen.getByTestId("selected-items")).toHaveTextContent("");
    });
  });

  describe("Width Classes", () => {
    test("should apply small width class for 200px", () => {
      const smallWidthProps = {
        ...defaultProps,
        config: { ...defaultProps.config, width: "200px" },
      };

      const { container } = render(
        <CardDropdownSelectGroup {...smallWidthProps} />
      );
      const wrapper = container.querySelector(".raSelectWrapper");

      expect(wrapper).toHaveClass("dropdownWidthSmall");
    });

    test("should apply medium width class for 300px", () => {
      const mediumWidthProps = {
        ...defaultProps,
        config: { ...defaultProps.config, width: "300px" },
      };

      const { container } = render(
        <CardDropdownSelectGroup {...mediumWidthProps} />
      );
      const wrapper = container.querySelector(".raSelectWrapper");

      expect(wrapper).toHaveClass("dropdownWidthMedium");
    });

    test("should apply large width class for 350px", () => {
      const largeWidthProps = {
        ...defaultProps,
        config: { ...defaultProps.config, width: "350px" },
      };

      const { container } = render(
        <CardDropdownSelectGroup {...largeWidthProps} />
      );
      const wrapper = container.querySelector(".raSelectWrapper");

      expect(wrapper).toHaveClass("dropdownWidthLarge");
    });

    test("should not apply width class for unknown width", () => {
      const unknownWidthProps = {
        ...defaultProps,
        config: { ...defaultProps.config, width: "400px" },
      };

      const { container } = render(
        <CardDropdownSelectGroup {...unknownWidthProps} />
      );
      const wrapper = container.querySelector(".raSelectWrapper");

      expect(wrapper).not.toHaveClass("dropdownWidthSmall");
      expect(wrapper).not.toHaveClass("dropdownWidthMedium");
      expect(wrapper).not.toHaveClass("dropdownWidthLarge");
    });

    test("should handle undefined width", () => {
      const undefinedWidthProps = {
        ...defaultProps,
        config: { placeholder: "Select Vessels" },
      };

      const { container } = render(
        <CardDropdownSelectGroup {...undefinedWidthProps} />
      );
      const wrapper = container.querySelector(".raSelectWrapper");

      expect(wrapper).toHaveClass("raSelectWrapper");
      expect(wrapper).not.toHaveClass("dropdownWidthSmall");
      expect(wrapper).not.toHaveClass("dropdownWidthMedium");
      expect(wrapper).not.toHaveClass("dropdownWidthLarge");
    });
  });

  describe("Selection Changes", () => {
    test("should call onChange with correct parameters when selection changes", () => {
      const mockOnChange = jest.fn();
      const changeProps = { ...defaultProps, onChange: mockOnChange };

      render(<CardDropdownSelectGroup {...changeProps} />);

      const changeButton = screen.getByTestId("trigger-selection-change");
      fireEvent.click(changeButton);

      expect(mockOnChange).toHaveBeenCalledWith(0, ["vessel1", "vessel2"]);
    });

    test("should call onChange with correct index for different components", () => {
      const mockOnChange = jest.fn();
      const changeProps = { ...defaultProps, index: 5, onChange: mockOnChange };

      render(<CardDropdownSelectGroup {...changeProps} />);

      const changeButton = screen.getByTestId("trigger-selection-change");
      fireEvent.click(changeButton);

      expect(mockOnChange).toHaveBeenCalledWith(5, ["vessel1", "vessel2"]);
    });

    test("should handle selection change callback memoization", () => {
      const mockOnChange = jest.fn();
      const { rerender } = render(
        <CardDropdownSelectGroup {...defaultProps} onChange={mockOnChange} />
      );

      // Rerender with same props
      rerender(
        <CardDropdownSelectGroup {...defaultProps} onChange={mockOnChange} />
      );

      const changeButton = screen.getByTestId("trigger-selection-change");
      fireEvent.click(changeButton);

      expect(mockOnChange).toHaveBeenCalledWith(0, ["vessel1", "vessel2"]);
    });
  });

  describe("Error Handling", () => {
    test("should return null and log error when groups prop is missing", () => {
      const consoleErrorSpy = jest
        .spyOn(console, "error")
        .mockImplementation(() => {});
      const propsWithoutGroups = { ...defaultProps, groups: undefined as any };

      const { container } = render(
        <CardDropdownSelectGroup {...propsWithoutGroups} />
      );

      expect(container.firstChild).toBeNull();
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "VesselSelectGroup: `groups` prop is missing."
      );
    });

    test("should return null when groups is null", () => {
      const consoleErrorSpy = jest
        .spyOn(console, "error")
        .mockImplementation(() => {});
      const propsWithNullGroups = { ...defaultProps, groups: null as any };

      const { container } = render(
        <CardDropdownSelectGroup {...propsWithNullGroups} />
      );

      expect(container.firstChild).toBeNull();
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        "VesselSelectGroup: `groups` prop is missing."
      );
    });

    test("should handle empty groups array", () => {
      const emptyGroupsProps = { ...defaultProps, groups: [] };

      render(<CardDropdownSelectGroup {...emptyGroupsProps} />);

      expect(screen.getByTestId("card-dropdown")).toBeInTheDocument();
    });
  });

  describe("Visibility Props", () => {
    test("should pass isSearchBoxVisible as false", () => {
      const noSearchProps = { ...defaultProps, isSearchBoxVisible: false };

      render(<CardDropdownSelectGroup {...noSearchProps} />);

      expect(screen.getByTestId("search-visible")).toHaveTextContent("false");
    });

    test("should pass isSelectAllVisible as false", () => {
      const noSelectAllProps = { ...defaultProps, isSelectAllVisible: false };

      render(<CardDropdownSelectGroup {...noSelectAllProps} />);

      expect(screen.getByTestId("select-all-visible")).toHaveTextContent(
        "false"
      );
    });
  });

  describe("Component Structure", () => {
    test("should render wrapper with correct CSS classes", () => {
      const { container } = render(
        <CardDropdownSelectGroup {...defaultProps} />
      );
      const wrapper = container.querySelector(".raSelectWrapper");

      expect(wrapper).toBeInTheDocument();
      expect(wrapper).toHaveClass("raSelectWrapper");
      expect(wrapper).toHaveClass("dropdownWidthSmall");
    });

    test("should contain CardDropdown as child", () => {
      render(<CardDropdownSelectGroup {...defaultProps} />);

      expect(screen.getByTestId("card-dropdown")).toBeInTheDocument();
    });
  });

  describe("React.memo Optimization", () => {
    test("should not re-render when props haven't changed", () => {
      const { rerender } = render(
        <CardDropdownSelectGroup {...defaultProps} />
      );

      // Rerender with same props
      rerender(<CardDropdownSelectGroup {...defaultProps} />);

      // Component should still be rendered correctly
      expect(screen.getByTestId("card-dropdown")).toBeInTheDocument();
    });

    test("should re-render when props change", () => {
      const { rerender } = render(
        <CardDropdownSelectGroup {...defaultProps} />
      );

      expect(screen.getByTestId("selected-items")).toHaveTextContent(
        "Vessel 1"
      );

      // Rerender with different selected items
      const newProps = {
        ...defaultProps,
        selectedItems: ["Vessel 2", "Vessel 3"],
      };
      rerender(<CardDropdownSelectGroup {...newProps} />);

      expect(screen.getByTestId("selected-items")).toHaveTextContent(
        "Vessel 2,Vessel 3"
      );
    });
  });

  describe("Edge Cases", () => {
    test("should handle very long vessel names", () => {
      const longNameProps = {
        ...defaultProps,
        selectedItems: ["Very Long Vessel Name That Exceeds Normal Length"],
      };

      render(<CardDropdownSelectGroup {...longNameProps} />);

      expect(screen.getByTestId("selected-items")).toHaveTextContent(
        "Very Long Vessel Name That Exceeds Normal Length"
      );
    });

    test("should handle special characters in vessel names", () => {
      const specialCharProps = {
        ...defaultProps,
        selectedItems: ["Vessel-1", "Vessel_2", "Vessel@3", "Vessel#4"],
      };

      render(<CardDropdownSelectGroup {...specialCharProps} />);

      expect(screen.getByTestId("selected-items")).toHaveTextContent(
        "Vessel-1,Vessel_2,Vessel@3,Vessel#4"
      );
    });

    test("should handle large number of groups", () => {
      const manyGroups = Array.from({ length: 100 }, (_, i) => ({
        id: i,
        title: `Group ${i}`,
        vessels: [
          {
            vessel_id: i * 10,
            name: `Vessel ${i}`,
            vessel_account_code_new: `V${i}`,
          },
        ],
      }));

      const manyGroupsProps = { ...defaultProps, groups: manyGroups };

      render(<CardDropdownSelectGroup {...manyGroupsProps} />);

      expect(screen.getByTestId("card-dropdown")).toBeInTheDocument();
    });

    test("should handle groups with no vessels", () => {
      const emptyVesselGroups = [
        { id: 1, title: "Empty Group", vessels: [] },
        { id: 2, title: "Another Empty Group", vessels: [] },
      ];

      const emptyVesselProps = { ...defaultProps, groups: emptyVesselGroups };

      render(<CardDropdownSelectGroup {...emptyVesselProps} />);

      expect(screen.getByTestId("card-dropdown")).toBeInTheDocument();
    });
  });

  describe("Accessibility", () => {
    test("should maintain proper component structure for screen readers", () => {
      const { container } = render(
        <CardDropdownSelectGroup {...defaultProps} />
      );

      expect(container.querySelector(".raSelectWrapper")).toBeInTheDocument();
      expect(screen.getByTestId("card-dropdown")).toBeInTheDocument();
    });
  });
});
