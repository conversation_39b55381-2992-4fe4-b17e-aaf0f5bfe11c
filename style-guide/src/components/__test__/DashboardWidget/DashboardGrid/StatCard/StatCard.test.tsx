import React from "react";
import { render, screen } from "@testing-library/react";
import StatCard from "../../../../DashboardWidget/common/DashboardGrid/StatCard/StatCard";

describe("StatCard", () => {
  it("renders without crashing", () => {
    render(<StatCard title="Users" value={100} />);
    expect(screen.getByText("Users")).toBeInTheDocument();
    expect(screen.getByText("100")).toBeInTheDocument();
  });

  it("displays the correct title", () => {
    render(<StatCard title="Revenue" value={5000} />);
    const titleElement = screen.getByText("Revenue");
    expect(titleElement).toBeInTheDocument();
    expect(titleElement).toHaveClass("stat-card-title");
  });

  it("displays the correct value", () => {
    render(<StatCard title="Orders" value={42} />);
    const valueElement = screen.getByText("42");
    expect(valueElement).toBeInTheDocument();
    expect(valueElement).toHaveClass("stat-card-value");
  });

  it("renders multiple StatCards independently", () => {
    render(
      <>
        <StatCard title="Users" value={100} />
        <StatCard title="Orders" value={42} />
      </>
    );
    expect(screen.getByText("Users")).toBeInTheDocument();
    expect(screen.getByText("100")).toBeInTheDocument();
    expect(screen.getByText("Orders")).toBeInTheDocument();
    expect(screen.getByText("42")).toBeInTheDocument();
  });
});
