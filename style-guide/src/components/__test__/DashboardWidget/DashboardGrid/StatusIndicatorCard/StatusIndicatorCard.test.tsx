// StatusIndicatorCard.test.tsx
import React from "react";
import { render, screen } from "@testing-library/react";
import StatusIndicatorCard from "../../../../DashboardWidget/common/DashboardGrid/StatusIndicatorCard/StatusIndicatorCard";

describe("StatusIndicatorCard", () => {
  it("renders value and label correctly", () => {
    render(<StatusIndicatorCard value={42} label="Completed" color="green" />);

    expect(screen.getByText("42")).toBeInTheDocument();
    expect(screen.getByText("Completed")).toBeInTheDocument();
  });

  it("applies the correct border color style", () => {
    render(<StatusIndicatorCard value={10} label="Pending" color="gold" />);
    const card = screen.getByText("Pending").closest(".status-indicator-card");

    expect(card).toHaveStyle("border-left: 5px solid gold");
  });

  it("renders with different colors dynamically", () => {
    const { rerender } = render(
      <StatusIndicatorCard value={5} label="Error" color="#e53935" />
    );
    let card = screen.getByText("Error").closest(".status-indicator-card");
    expect(card).toHaveStyle("border-left: 5px solid #e53935");

    rerender(<StatusIndicatorCard value={5} label="Warning" color="orange" />);
    card = screen.getByText("Warning").closest(".status-indicator-card");
    expect(card).toHaveStyle("border-left: 5px solid orange");
  });

  it("displays zero values correctly", () => {
    render(<StatusIndicatorCard value={0} label="None" color="gray" />);
    expect(screen.getByText("0")).toBeInTheDocument();
    expect(screen.getByText("None")).toBeInTheDocument();
  });
});
