// Dashboard.test.tsx
import React from "react";
import { render, screen } from "@testing-library/react";
import { Vessel } from "../../../DashboardWidget/types/card-types";
import Dashboard from "../../../DashboardWidget/common/DashboardGrid/Dashboard";

const mockVessels: Vessel[] = [
  {
    name: "Vessel A",
    vesselData: ["x", "Critical", "Approved"],
    type: "Type1",
    ra_level: "Critical",
  },
  {
    name: "Vessel B",
    vesselData: ["y", "Critical", "Pending"],
    type: "Type1",
    ra_level: "Critical",
  },
  {
    name: "Vessel C",
    vesselData: ["z", "Special", "Rejected"],
    type: "Type2",
    ra_level: "Special",
  },
  {
    name: "Vessel D",
    vesselData: ["w", "Unassigned", "Approved"],
    type: "Type2",
    ra_level: "Unassigned",
  },
];

describe("Dashboard", () => {
  it("renders without crashing (empty vessels)", () => {
    render(
      <Dashboard
        vessels={[]}
        badgeColors={["green", "gold", "orange", "red"]}
      />
    );
    expect(
      screen.queryByText(/Total Risk Assessment Submitted/i)
    ).not.toBeInTheDocument();
  });

  it("applies correct colors to statuses", () => {
    const colors = ["green", "gold", "orange", "red"];
    render(<Dashboard vessels={mockVessels} badgeColors={colors} />);

    const approvedCard = screen
      .getByText("Approved")
      .closest(".status-indicator-card");
    expect(approvedCard).toHaveStyle(`border-left: 5px solid ${colors[0]}`);

    const pendingCard = screen
      .getByText("Pending")
      .closest(".status-indicator-card");
    expect(pendingCard).toHaveStyle(`border-left: 5px solid ${colors[2]}`);

    const rejectedCard = screen
      .getByText("Rejected")
      .closest(".status-indicator-card");
    expect(rejectedCard).toHaveStyle(`border-left: 5px solid ${colors[3]}`);
  });

  it("does not create a section for Unassigned vessels", () => {
    render(
      <Dashboard
        vessels={mockVessels}
        badgeColors={["green", "gold", "orange", "red"]}
      />
    );
    expect(screen.queryByText("UNASSIGNED")).not.toBeInTheDocument();
  });
});
