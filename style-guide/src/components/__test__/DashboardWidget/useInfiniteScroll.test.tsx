import { renderHook, act } from "@testing-library/react";
import "@testing-library/jest-dom";
import { useInfiniteScroll } from "../../DashboardWidget/hooks/useInfiniteScroll";

// Mock IntersectionObserver
const mockIntersectionObserver = jest.fn();
mockIntersectionObserver.mockReturnValue({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});
window.IntersectionObserver = mockIntersectionObserver;

describe("useInfiniteScroll Hook", () => {
  const mockFetchNextPage = jest.fn();

  const defaultProps = {
    fetchNextPage: mockFetchNextPage,
    isFetchingNextPage: false,
    hasNextPage: true,
    dataLength: 10,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("returns containerRef and handleScroll function", () => {
    const { result } = renderHook(() => useInfiniteScroll(defaultProps));

    expect(result.current.containerRef).toBeDefined();
    expect(result.current.handleScroll).toBeDefined();
    expect(typeof result.current.handleScroll).toBe("function");
  });

  test("does not fetch when already fetching", () => {
    const props = {
      ...defaultProps,
      isFetchingNextPage: true,
    };

    const { result } = renderHook(() => useInfiniteScroll(props));

    // Mock the containerRef to have the element
    const mockElement = {
      scrollTop: 100,
      scrollHeight: 200,
      clientHeight: 50,
    };
    Object.defineProperty(result.current.containerRef, 'current', {
      writable: true,
      value: mockElement,
    });

    // Simulate scroll event
    act(() => {
      result.current.handleScroll();
    });

    expect(mockFetchNextPage).not.toHaveBeenCalled();
  });

  test("does not fetch when no next page available", () => {
    const props = {
      ...defaultProps,
      hasNextPage: false,
    };

    const { result } = renderHook(() => useInfiniteScroll(props));

    // Mock the containerRef to have the element
    const mockElement = {
      scrollTop: 100,
      scrollHeight: 200,
      clientHeight: 50,
    };
    Object.defineProperty(result.current.containerRef, 'current', {
      writable: true,
      value: mockElement,
    });

    act(() => {
      result.current.handleScroll();
    });

    expect(mockFetchNextPage).not.toHaveBeenCalled();
  });

  test("does not fetch when not scrolled enough", () => {
    const { result } = renderHook(() => useInfiniteScroll(defaultProps));

    // Mock the containerRef to have the element
    const mockElement = {
      scrollTop: 100, // Not near bottom (1000 - 200 - 300 = 500, so 100 < 500)
      scrollHeight: 1000,
      clientHeight: 200,
    };
    Object.defineProperty(result.current.containerRef, 'current', {
      writable: true,
      value: mockElement,
    });

    act(() => {
      result.current.handleScroll();
    });

    expect(mockFetchNextPage).not.toHaveBeenCalled();
  });

  test("fetches when scrolled near bottom", () => {
    const { result } = renderHook(() => useInfiniteScroll(defaultProps));

    // Mock the containerRef to have the element
    const mockElement = {
      scrollTop: 750, // Near bottom (1000 - 750 - 200 = 50, which is < 300 threshold)
      scrollHeight: 1000,
      clientHeight: 200,
    };
    Object.defineProperty(result.current.containerRef, 'current', {
      writable: true,
      value: mockElement,
    });

    act(() => {
      result.current.handleScroll();
    });

    expect(mockFetchNextPage).toHaveBeenCalledTimes(1);
  });

  test("does not fetch when containerRef is null", () => {
    const { result } = renderHook(() => useInfiniteScroll(defaultProps));

    // containerRef.current is null by default
    act(() => {
      result.current.handleScroll();
    });

    expect(mockFetchNextPage).not.toHaveBeenCalled();
  });

  test("handles fetchNextPage not being a function", () => {
    const propsWithInvalidFetch = {
      ...defaultProps,
      fetchNextPage: null as any,
    };

    const { result } = renderHook(() => useInfiniteScroll(propsWithInvalidFetch));

    const mockElement = {
      scrollTop: 750,
      scrollHeight: 1000,
      clientHeight: 200,
    };
    Object.defineProperty(result.current.containerRef, 'current', {
      writable: true,
      value: mockElement,
    });

    // Should not throw error
    expect(() => {
      act(() => {
        result.current.handleScroll();
      });
    }).not.toThrow();
  });

  test("handles edge case where scrollHeight equals clientHeight", () => {
    const { result } = renderHook(() => useInfiniteScroll(defaultProps));

    const mockElement = {
      scrollTop: 0,
      scrollHeight: 200,
      clientHeight: 200,
    };
    Object.defineProperty(result.current.containerRef, 'current', {
      writable: true,
      value: mockElement,
    });

    act(() => {
      result.current.handleScroll();
    });

    // Should fetch because scrollHeight - scrollTop - clientHeight = 0, which is < 300
    expect(mockFetchNextPage).toHaveBeenCalledTimes(1);
  });

  test("handles multiple rapid scroll events", () => {
    const { result } = renderHook(() => useInfiniteScroll(defaultProps));

    const mockElement = {
      scrollTop: 750,
      scrollHeight: 1000,
      clientHeight: 200,
    };
    Object.defineProperty(result.current.containerRef, 'current', {
      writable: true,
      value: mockElement,
    });

    // Simulate multiple rapid scroll events
    act(() => {
      result.current.handleScroll();
      result.current.handleScroll();
      result.current.handleScroll();
    });

    // Should only fetch once per scroll event
    expect(mockFetchNextPage).toHaveBeenCalledTimes(3);
  });

  test("updates when props change", () => {
    const { result, rerender } = renderHook(
      (props) => useInfiniteScroll(props),
      { initialProps: defaultProps }
    );

    // Initial render
    expect(result.current.containerRef).toBeDefined();

    // Update props
    const newProps = {
      ...defaultProps,
      hasNextPage: false,
    };

    rerender(newProps);

    const mockElement = {
      scrollTop: 750,
      scrollHeight: 1000,
      clientHeight: 200,
    };
    Object.defineProperty(result.current.containerRef, 'current', {
      writable: true,
      value: mockElement,
    });

    act(() => {
      result.current.handleScroll();
    });

    // Should not fetch because hasNextPage is false
    expect(mockFetchNextPage).not.toHaveBeenCalled();
  });

  test("handles dataLength changes for initial fetch check", () => {
    jest.useFakeTimers();

    const { result, rerender } = renderHook(
      (props) => useInfiniteScroll(props),
      { initialProps: defaultProps }
    );

    // Mock element with short content
    const mockElement = {
      scrollHeight: 100,
      clientHeight: 200,
    };
    Object.defineProperty(result.current.containerRef, 'current', {
      writable: true,
      value: mockElement,
    });

    // Update dataLength to trigger useEffect
    rerender({ ...defaultProps, dataLength: 20 });

    // Fast-forward timers to trigger the timeout
    act(() => {
      jest.advanceTimersByTime(150);
    });

    // Should fetch because content is shorter than viewport
    expect(mockFetchNextPage).toHaveBeenCalled();

    jest.useRealTimers();
  });

  test("does not fetch initially when content fills viewport", () => {
    jest.useFakeTimers();

    const { result } = renderHook(() => useInfiniteScroll(defaultProps));

    // Mock element with content that fills viewport
    const mockElement = {
      scrollHeight: 500,
      clientHeight: 200,
    };
    Object.defineProperty(result.current.containerRef, 'current', {
      writable: true,
      value: mockElement,
    });

    // Fast-forward timers
    act(() => {
      jest.advanceTimersByTime(150);
    });

    // Should not fetch because content fills viewport
    expect(mockFetchNextPage).not.toHaveBeenCalled();

    jest.useRealTimers();
  });

  test("cleans up timeout on unmount", () => {
    jest.useFakeTimers();
    const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout');

    const { unmount } = renderHook(() => useInfiniteScroll(defaultProps));

    unmount();

    expect(clearTimeoutSpy).toHaveBeenCalled();

    clearTimeoutSpy.mockRestore();
    jest.useRealTimers();
  });

  test("handles threshold boundary conditions", () => {
    const { result } = renderHook(() => useInfiniteScroll(defaultProps));

    // Test exactly at threshold (300px from bottom)
    const mockElement = {
      scrollTop: 500, // 1000 - 500 - 200 = 300 (exactly at threshold)
      scrollHeight: 1000,
      clientHeight: 200,
    };
    Object.defineProperty(result.current.containerRef, 'current', {
      writable: true,
      value: mockElement,
    });

    act(() => {
      result.current.handleScroll();
    });

    // Should not fetch because it's exactly at threshold, not below
    expect(mockFetchNextPage).not.toHaveBeenCalled();

    // Test just below threshold
    mockElement.scrollTop = 501; // 1000 - 501 - 200 = 299 (below threshold)

    act(() => {
      result.current.handleScroll();
    });

    expect(mockFetchNextPage).toHaveBeenCalledTimes(1);
  });

  test("handles zero dimensions gracefully", () => {
    const { result } = renderHook(() => useInfiniteScroll(defaultProps));

    const mockElement = {
      scrollTop: 0,
      scrollHeight: 0,
      clientHeight: 0,
    };
    Object.defineProperty(result.current.containerRef, 'current', {
      writable: true,
      value: mockElement,
    });

    expect(() => {
      act(() => {
        result.current.handleScroll();
      });
    }).not.toThrow();

    expect(mockFetchNextPage).toHaveBeenCalledTimes(1);
  });
});
