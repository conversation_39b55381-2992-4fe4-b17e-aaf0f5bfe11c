import { renderHook, act } from "@testing-library/react";
import "@testing-library/jest-dom";
import { useInfiniteScroll } from "../../DashboardWidget/hooks/useInfiniteScroll";

// Mock IntersectionObserver
const mockIntersectionObserver = jest.fn();
mockIntersectionObserver.mockReturnValue({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});
window.IntersectionObserver = mockIntersectionObserver;

describe("useInfiniteScroll Hook", () => {
  const mockFetchNextPage = jest.fn();

  const defaultProps = {
    fetchNextPage: mockFetchNextPage,
    isFetchingNextPage: false,
    hasNextPage: true,
    dataLength: 10,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("returns containerRef and handleScroll function", () => {
    const { result } = renderHook(() => useInfiniteScroll(defaultProps));

    expect(result.current.containerRef).toBeDefined();
    expect(result.current.handleScroll).toBeDefined();
    expect(typeof result.current.handleScroll).toBe("function");
  });

  test("does not fetch when already fetching", () => {
    const props = {
      ...defaultProps,
      isFetchingNextPage: true,
    };

    const { result } = renderHook(() => useInfiniteScroll(props));

    // Mock the containerRef to have the element
    const mockElement = {
      scrollTop: 100,
      scrollHeight: 200,
      clientHeight: 50,
    };
    Object.defineProperty(result.current.containerRef, 'current', {
      writable: true,
      value: mockElement,
    });

    // Simulate scroll event
    act(() => {
      result.current.handleScroll();
    });

    expect(mockFetchNextPage).not.toHaveBeenCalled();
  });

  test("does not fetch when no next page available", () => {
    const props = {
      ...defaultProps,
      hasNextPage: false,
    };

    const { result } = renderHook(() => useInfiniteScroll(props));

    // Mock the containerRef to have the element
    const mockElement = {
      scrollTop: 100,
      scrollHeight: 200,
      clientHeight: 50,
    };
    Object.defineProperty(result.current.containerRef, 'current', {
      writable: true,
      value: mockElement,
    });

    act(() => {
      result.current.handleScroll();
    });

    expect(mockFetchNextPage).not.toHaveBeenCalled();
  });

  test("does not fetch when not scrolled enough", () => {
    const { result } = renderHook(() => useInfiniteScroll(defaultProps));

    // Mock the containerRef to have the element
    const mockElement = {
      scrollTop: 100, // Not near bottom (1000 - 200 - 300 = 500, so 100 < 500)
      scrollHeight: 1000,
      clientHeight: 200,
    };
    Object.defineProperty(result.current.containerRef, 'current', {
      writable: true,
      value: mockElement,
    });

    act(() => {
      result.current.handleScroll();
    });

    expect(mockFetchNextPage).not.toHaveBeenCalled();
  });
});
