import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import CardModule from '../../DashboardWidget/iCard/CardModule';

// Mock child components

jest.mock('../../DashboardWidget/iCard/CardGrid', () => {
  return function MockCardGrid(props: any) {
    return <div data-testid="card-grid">Grid View - {props.vessels.length} vessels</div>;
  };
});

jest.mock('../../DashboardWidget/iCard/CardModuleHeader', () => ({
  CardModuleHeader: (props: any) => (
    <div data-testid="card-module-header">
      <button onClick={() => props.onViewModeChange('list')}>List</button>
      <button onClick={() => props.onViewModeChange('grid')}>Grid</button>
      <button onClick={props.onToggleModal}>Toggle Modal</button>
    </div>
  ),
}));

jest.mock('../../DashboardWidget/iCard/CardDropdownSelectors', () => ({
  CardDropdownSelectors: () => <div data-testid="card-dropdown-selectors">Dropdowns</div>,
}));

jest.mock('../../DashboardWidget/iCard/ModuleModal', () => ({
  ModuleModal: ({ children, isOpen }: any) => 
    isOpen ? <div data-testid="module-modal">{children}</div> : null,
}));

jest.mock('../../DashboardWidget/iCard/CardTabs', () => ({
  CardTabs: (props: any) => (
    <div data-testid="card-tabs">
      {props.tabs.map((tab: string) => (
        <button key={tab} onClick={() => props.onTabChange(tab)}>
          {tab}
        </button>
      ))}
    </div>
  ),
}));

// Mock CardList component
jest.mock('../../DashboardWidget/iCard/CardList', () => {
  return function MockCardList(props: any) {
    return <div data-testid="card-list">List View - {props.vessels.length} vessels</div>;
  };
});

// Mock useMediaQuery hook
jest.mock('../../DashboardWidget/hooks/useMediaQuery', () => ({
  useMediaQuery: jest.fn(() => false),
}));

const mockProps = {
  title: 'Test Dashboard',
  vessels: [
    { name: 'Vessel A', status: 'Active', vesselData: [5, 10] },
    { name: 'Vessel B', status: 'Inactive', vesselData: [8, 12] },
  ],
  staticData: {
    tabs: ['Active', 'Inactive', 'All'],
    tableHeaders: ['Vessel', 'Status A', 'Status B', 'Action'],
    badgeColors: ['#ff0000', '#00ff00'],
    configKey: 'test-config',
    severityData: { high: 5, medium: 3, low: 2 },
  },
  visibleConfig: {
    IsAllTabVisible: true,
    IsLastUpdatedVisible: true,
    IsRefereshIconVisible: true,
    IsDropdownVisible: true,
    IsTabsVisible: true,
    IsiconRenderVisible: true,
    IsenLargeIconVisible: true,
    filterApplyonRenderData: 'all',
  },
  multiVesselSelects: [],
  componentView: {
    defaultComponent: 'list' as const,
    gridComponent: 'bar' as const,
  },
  sizeKey: 'md' as const,
  onRefresh: jest.fn(),
  onSendEmail: jest.fn(),
  onVesselClick: jest.fn(),
  onChangeActiveTab: jest.fn(),
  fetchNextPage: jest.fn(),
  isFetchingNextPage: false,
  isLoading: false,
  pagination: { page: 1, totalPages: 1 },
  columns: [],
  responsive: false,
  responsiveConfig: {
    designName: 'test-design',
    component: () => <div>Responsive Component</div>,
  },
};

const CardModuleWithRouter = (props: any) => (
  <BrowserRouter>
    <CardModule {...props} />
  </BrowserRouter>
);

describe('CardModule Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders without crashing', () => {
    render(<CardModuleWithRouter {...mockProps} />);
    expect(screen.getByTestId('card-module-header')).toBeInTheDocument();
  });

  test('renders in list view by default', () => {
    render(<CardModuleWithRouter {...mockProps} />);
    expect(screen.getByTestId('card-list')).toBeInTheDocument();
    expect(screen.queryByTestId('card-grid')).not.toBeInTheDocument();
  });

  test('switches to grid view when button is clicked', () => {
    render(<CardModuleWithRouter {...mockProps} />);
    
    fireEvent.click(screen.getByText('Grid'));
    
    expect(screen.getByTestId('card-grid')).toBeInTheDocument();
    expect(screen.queryByTestId('card-table')).not.toBeInTheDocument();
  });

  test('switches back to list view', () => {
    render(<CardModuleWithRouter {...mockProps} />);
    
    // Switch to grid first
    fireEvent.click(screen.getByText('Grid'));
    expect(screen.getByTestId('card-grid')).toBeInTheDocument();
    
    // Switch back to list
    fireEvent.click(screen.getByText('List'));
    expect(screen.getByTestId('card-list')).toBeInTheDocument();
    expect(screen.queryByTestId('card-grid')).not.toBeInTheDocument();
  });

  test('opens modal when toggle button is clicked', () => {
    render(<CardModuleWithRouter {...mockProps} />);
    
    fireEvent.click(screen.getByText('Toggle Modal'));
    
    expect(screen.getByTestId('module-modal')).toBeInTheDocument();
  });

  test('renders with grid view as default when specified', () => {
    const gridProps = {
      ...mockProps,
      componentView: {
        defaultComponent: 'grid' as const,
        gridComponent: 'bar' as const,
      },
    };
    
    render(<CardModuleWithRouter {...gridProps} />);
    expect(screen.getByTestId('card-grid')).toBeInTheDocument();
  });

  test('handles refresh callback', () => {
    render(<CardModuleWithRouter {...mockProps} />);
    
    // This would be triggered by the refresh icon in the header
    expect(mockProps.onRefresh).not.toHaveBeenCalled();
  });

  test('passes correct props to child components', () => {
    render(<CardModuleWithRouter {...mockProps} />);

    // Check that vessels are passed correctly
    expect(screen.getByText('List View - 2 vessels')).toBeInTheDocument();
  });

  // Additional comprehensive test cases
  test('renders with empty vessels array', () => {
    const emptyProps = { ...mockProps, vessels: [] };
    render(<CardModuleWithRouter {...emptyProps} />);

    expect(screen.getByTestId('card-module-header')).toBeInTheDocument();
    expect(screen.getByText('List View - 0 vessels')).toBeInTheDocument();
  });

  test('renders with different size keys', () => {
    const smallProps = { ...mockProps, sizeKey: 'sm' as const };
    render(<CardModuleWithRouter {...smallProps} />);

    const container = document.querySelector('.ra-vessel-card-container.size-sm');
    expect(container).toBeInTheDocument();
  });

  test('renders with large size key', () => {
    const largeProps = { ...mockProps, sizeKey: 'lg' as const };
    render(<CardModuleWithRouter {...largeProps} />);

    const container = document.querySelector('.ra-vessel-card-container.size-lg');
    expect(container).toBeInTheDocument();
  });

  test('handles loading state', () => {
    const loadingProps = { ...mockProps, isLoading: true };
    render(<CardModuleWithRouter {...loadingProps} />);

    expect(screen.getByTestId('card-list')).toBeInTheDocument();
  });

  test('handles fetching next page state', () => {
    const fetchingProps = { ...mockProps, isFetchingNextPage: true };
    render(<CardModuleWithRouter {...fetchingProps} />);

    expect(screen.getByTestId('card-list')).toBeInTheDocument();
  });

  test('renders last updated section when visible', () => {
    const visibleLastUpdatedProps = {
      ...mockProps,
      visibleConfig: {
        ...mockProps.visibleConfig,
        IsLastUpdatedVisible: true,
      },
    };

    render(<CardModuleWithRouter {...visibleLastUpdatedProps} />);
    expect(screen.getByText(/Last Updated on:/)).toBeInTheDocument();
  });

  test('hides last updated section when not visible', () => {
    const hiddenLastUpdatedProps = {
      ...mockProps,
      visibleConfig: {
        ...mockProps.visibleConfig,
        IsLastUpdatedVisible: false,
      },
    };

    render(<CardModuleWithRouter {...hiddenLastUpdatedProps} />);
    expect(screen.queryByText(/Last Updated on:/)).not.toBeInTheDocument();
  });

  test('renders refresh icon when visible', () => {
    const visibleRefreshProps = {
      ...mockProps,
      visibleConfig: {
        ...mockProps.visibleConfig,
        IsLastUpdatedVisible: true,
        IsRefereshIconVisible: true,
      },
    };

    render(<CardModuleWithRouter {...visibleRefreshProps} />);
    const refreshIcon = document.querySelector('.ra-refresh-icon');
    expect(refreshIcon).toBeInTheDocument();
  });

  test('calls onRefresh when refresh icon is clicked', () => {
    const onRefreshSpy = jest.fn();
    const refreshProps = {
      ...mockProps,
      onRefresh: onRefreshSpy,
      visibleConfig: {
        ...mockProps.visibleConfig,
        IsLastUpdatedVisible: true,
        IsRefereshIconVisible: true,
      },
    };

    render(<CardModuleWithRouter {...refreshProps} />);
    const refreshIcon = document.querySelector('.ra-refresh-icon');

    if (refreshIcon) {
      fireEvent.click(refreshIcon);
      expect(onRefreshSpy).toHaveBeenCalledTimes(1);
    }
  });

  test('closes modal when clicking outside', () => {
    render(<CardModuleWithRouter {...mockProps} />);

    // Open modal first
    fireEvent.click(screen.getByText('Toggle Modal'));
    expect(screen.getByTestId('module-modal')).toBeInTheDocument();

    // Close modal by clicking toggle again (use getAllByText to handle multiple instances)
    const toggleButtons = screen.getAllByText('Toggle Modal');
    fireEvent.click(toggleButtons[0]); // Click the first one (main component)
    expect(screen.queryByTestId('module-modal')).not.toBeInTheDocument();
  });

  test('renders with responsive configuration', () => {
    const responsiveProps = {
      ...mockProps,
      responsive: true,
      responsiveCardContainerHeight: '500px',
    };

    render(<CardModuleWithRouter {...responsiveProps} />);
    expect(screen.getByTestId('card-module-header')).toBeInTheDocument();
  });

  test('renders dropdown selectors when visible', () => {
    const dropdownProps = {
      ...mockProps,
      visibleConfig: {
        ...mockProps.visibleConfig,
        IsVesselSelectVisible: true,
        vesselSelectPosition: 'before' as const,
      },
      multiVesselSelects: [
        { id: 1, title: 'Test Select', groups: [] },
      ],
    };

    render(<CardModuleWithRouter {...dropdownProps} />);
    expect(screen.getByTestId('card-dropdown-selectors')).toBeInTheDocument();
  });

  test('renders tabs when visible', () => {
    const tabsProps = {
      ...mockProps,
      visibleConfig: {
        ...mockProps.visibleConfig,
        IsAlltabsVisible: true,
      },
    };

    render(<CardModuleWithRouter {...tabsProps} />);
    expect(screen.getByTestId('card-tabs')).toBeInTheDocument();
  });

  test('handles tab change callback', () => {
    const onChangeActiveTabSpy = jest.fn();
    const tabsProps = {
      ...mockProps,
      onChangeActiveTab: onChangeActiveTabSpy,
      visibleConfig: {
        ...mockProps.visibleConfig,
        IsAlltabsVisible: true,
      },
    };

    render(<CardModuleWithRouter {...tabsProps} />);

    // Click on a tab
    fireEvent.click(screen.getByText('Active'));
    expect(onChangeActiveTabSpy).toHaveBeenCalledWith('Active');
  });

  test('handles pagination correctly', () => {
    const paginationProps = {
      ...mockProps,
      pagination: { page: 2, totalPages: 5 },
    };

    render(<CardModuleWithRouter {...paginationProps} />);
    expect(screen.getByTestId('card-list')).toBeInTheDocument();
  });

  test('renders with multiVesselSelects configuration', () => {
    const multiSelectProps = {
      ...mockProps,
      multiVesselSelects: [
        { id: 1, title: 'Select 1', options: [] },
        { id: 2, title: 'Select 2', options: [] },
      ],
    };

    render(<CardModuleWithRouter {...multiSelectProps} />);
    expect(screen.getByTestId('card-module-header')).toBeInTheDocument();
  });

  test('handles vessel click callback', () => {
    const onVesselClickSpy = jest.fn();
    const vesselClickProps = {
      ...mockProps,
      onVesselClick: onVesselClickSpy,
    };

    render(<CardModuleWithRouter {...vesselClickProps} />);
    expect(screen.getByTestId('card-list')).toBeInTheDocument();
  });

  test('handles send email callback', () => {
    const onSendEmailSpy = jest.fn();
    const emailProps = {
      ...mockProps,
      onSendEmail: onSendEmailSpy,
    };

    render(<CardModuleWithRouter {...emailProps} />);
    expect(screen.getByTestId('card-list')).toBeInTheDocument();
  });

  test('renders modal with correct size class', () => {
    const modalProps = { ...mockProps, sizeKey: 'lg' as const };
    render(<CardModuleWithRouter {...modalProps} />);

    // Open modal
    fireEvent.click(screen.getByText('Toggle Modal'));

    // Check if modal is rendered
    expect(screen.getByTestId('module-modal')).toBeInTheDocument();
  });
});
