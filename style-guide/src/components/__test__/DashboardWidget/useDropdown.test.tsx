import { renderHook, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useDropdown } from '../../DashboardWidget/hooks/useDropdown';

describe('useDropdown Hook', () => {
  test('initializes with closed state', () => {
    const { result } = renderHook(() => useDropdown());

    expect(result.current.isOpen).toBe(false);
    expect(result.current.dropdownRef).toBeDefined();
    expect(result.current.toggleDropdown).toBeDefined();
  });

  test('opens dropdown when toggle is called', () => {
    const { result } = renderHook(() => useDropdown());

    act(() => {
      result.current.toggleDropdown();
    });

    expect(result.current.isOpen).toBe(true);
  });

  test('closes dropdown when toggle is called again', () => {
    const { result } = renderHook(() => useDropdown());

    // Open first
    act(() => {
      result.current.toggleDropdown();
    });
    expect(result.current.isOpen).toBe(true);

    // Close
    act(() => {
      result.current.toggleDropdown();
    });
    expect(result.current.isOpen).toBe(false);
  });

  test('provides stable function references', () => {
    const { result, rerender } = renderHook(() => useDropdown());

    const initialToggle = result.current.toggleDropdown;
    const initialRef = result.current.dropdownRef;

    // Trigger a re-render
    rerender();

    expect(result.current.toggleDropdown).toBe(initialToggle);
    expect(result.current.dropdownRef).toBe(initialRef);
  });

  test('does not close when clicking inside dropdown', () => {
    const { result } = renderHook(() => useDropdown());

    // Create a mock element for the dropdown and add it to DOM
    const mockElement = document.createElement('div');
    document.body.appendChild(mockElement);
    // Use Object.defineProperty to make the ref writable
    Object.defineProperty(result.current.dropdownRef, 'current', {
      writable: true,
      value: mockElement,
    });

    // Open dropdown
    act(() => {
      result.current.toggleDropdown();
    });
    expect(result.current.isOpen).toBe(true);

    // Simulate click inside dropdown
    act(() => {
      const event = new MouseEvent('mousedown', {
        bubbles: true,
        cancelable: true,
      });
      Object.defineProperty(event, 'target', {
        value: mockElement,
        enumerable: true,
      });
      document.dispatchEvent(event);
    });

    expect(result.current.isOpen).toBe(true);

    // Cleanup
    document.body.removeChild(mockElement);
  });
});
