import React from "react";
import { render, screen } from "@testing-library/react";
import BarChartSkeletonLoader from "./../../DashboardWidget/iCard/BarChartSkeletonLoader";

describe("BarChartSkeletonLoader", () => {
  it("renders without crashing", () => {
    render(<BarChartSkeletonLoader />);
    expect(screen.getByText("Overdue")).toBeInTheDocument();
    expect(screen.getByText("Due within 30 Days")).toBeInTheDocument();
    expect(screen.getByText("Due within 60 Days")).toBeInTheDocument();
  });

  it("renders 10 y-axis ticks", () => {
    render(<BarChartSkeletonLoader />);
    const yTicks = document.querySelectorAll(".y-axis-tick");
    expect(yTicks).toHaveLength(10);
  });

  it("renders 5 bar rows", () => {
    render(<BarChartSkeletonLoader />);
    const barRows = document.querySelectorAll(".bar-row");
    expect(barRows).toHaveLength(5);

    // each row should contain a label loader and a bar loader
    barRows.forEach((row) => {
      expect(row.querySelector(".label-loader")).toBeInTheDocument();
      expect(row.querySelector(".bar-loader")).toBeInTheDocument();
    });
  });

  it("renders x-axis labels correctly", () => {
    render(<BarChartSkeletonLoader />);
    const labels = Array.from(document.querySelectorAll(".x-axis-label")).map(
      (el) => el.textContent
    );

    expect(labels).toEqual([
      "0",
      "25",
      "50",
      "75",
      "100",
      "125",
      "150",
      "175",
      "200",
      "225",
    ]);
  });

  it("renders legend items with correct color-box classes", () => {
    render(<BarChartSkeletonLoader />);
    expect(document.querySelector(".color-box.overdue")).toBeInTheDocument();
    expect(document.querySelector(".color-box.due-30")).toBeInTheDocument();
    expect(document.querySelector(".color-box.due-60")).toBeInTheDocument();
  });
});
