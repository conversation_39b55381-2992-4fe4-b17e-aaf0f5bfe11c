import React from "react";
import { render, screen, fireEvent, within } from "@testing-library/react";
import { CardDropdown } from "../../DashboardWidget/iCard/CardDropdown";
import { VesselGroup } from "../../DashboardWidget/types/card-types";

// Mock CardDropdownMenu component
jest.mock("../../DashboardWidget/iCard/CardDropdownMenu", () => ({
  CardDropdownMenu: () => <div data-testid="dropdown-menu">Menu Content</div>,
}));

const mockToggle = jest.fn();
let mockIsOpen = false;

// Mock useDropdown hook
jest.mock("../../DashboardWidget/hooks/useDropdown", () => ({
  useDropdown: () => ({
    dropdownRef: { current: null },
    isOpen: mockIsOpen,
    toggleDropdown: mockToggle,
  }),
}));

const mockGroups: VesselGroup[] = [
  {
    id: 1,
    title: "Group 1",
    vessels: [
      {
        name: "Vessel 1",
        vessel_id: 1,
        vessel_account_code_new: "VAC1",
      },
      {
        name: "Vessel 2",
        vessel_id: 2,
        vessel_account_code_new: "VAC2",
      },
    ],
  },
  {
    id: 2,
    title: "Group 2",
    vessels: [
      {
        name: "Vessel 3",
        vessel_id: 3,
        vessel_account_code_new: "VAC3",
      },
      {
        name: "Vessel 4",
        vessel_id: 4,
        vessel_account_code_new: "VAC4",
      },
    ],
  },
];

describe("CardDropdown", () => {
  const defaultProps = {
    groups: mockGroups,
    selectedItems: [] as string[],
    onSelectionChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockIsOpen = false;
  });

  it("renders with default placeholder", () => {
    render(<CardDropdown {...defaultProps} />);
    expect(screen.getByText("My Vessels")).toBeInTheDocument();
  });

  it("renders with custom placeholder", () => {
    render(<CardDropdown {...defaultProps} placeholder="Select Vessels" />);
    expect(screen.getByText("Select Vessels")).toBeInTheDocument();
  });

  it("displays selected vessels correctly", () => {
    const props = {
      ...defaultProps,
      selectedItems: ["Vessel 1", "Vessel 2"],
    };
    render(<CardDropdown {...props} />);
    expect(screen.getByText("Vessel 1, Vessel 2")).toBeInTheDocument();
  });

  it("toggles dropdown when clicked", () => {
    render(<CardDropdown {...defaultProps} />);

    const dropdownHeader = screen.getByRole("button");
    fireEvent.click(dropdownHeader);

    expect(mockToggle).toHaveBeenCalled();
  });

  it("shows dropdown menu when open", () => {
    mockIsOpen = true;
    render(<CardDropdown {...defaultProps} />);

    expect(screen.getByTestId("dropdown-menu")).toBeInTheDocument();
  });

  it("hides dropdown menu when closed", () => {
    mockIsOpen = false;
    render(<CardDropdown {...defaultProps} />);

    expect(screen.queryByTestId("dropdown-menu")).not.toBeInTheDocument();
  });

  it("handles selection change callback", () => {
    const onSelectionChange = jest.fn();
    mockIsOpen = true;

    render(
      <CardDropdown {...defaultProps} onSelectionChange={onSelectionChange} />
    );

    const dropdownHeader = screen.getByRole("button");
    fireEvent.click(dropdownHeader);

    expect(mockToggle).toHaveBeenCalled();
  });

  it("applies chevron rotation when open", () => {
    mockIsOpen = true;
    render(<CardDropdown {...defaultProps} />);

    const chevron = screen.getByLabelText("toggle-chevron");
    expect(chevron).toHaveClass("raChevron", "raRotate");
  });
});
