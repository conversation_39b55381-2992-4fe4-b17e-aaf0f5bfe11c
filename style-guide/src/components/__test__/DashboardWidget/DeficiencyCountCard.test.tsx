// DeficiencyCountCard.test.tsx
import React from "react";
import { render, screen } from "@testing-library/react";
import { DeficiencyCountCard } from "./../../DashboardWidget/common/DeficiencyCountCard";

describe("DeficiencyCountCard", () => {
  it("renders the title", () => {
    render(<DeficiencyCountCard title="Open Deficiencies" total={5} />);
    expect(screen.getByText("Open Deficiencies")).toBeInTheDocument();
  });

  it("renders the total count", () => {
    render(<DeficiencyCountCard title="Closed Deficiencies" total={12} />);
    expect(screen.getByText("12")).toBeInTheDocument();
  });

  it("renders 0 properly", () => {
    render(<DeficiencyCountCard title="No Deficiencies" total={0} />);
    expect(screen.getByText("0")).toBeInTheDocument();
  });

  it("renders negative numbers if provided", () => {
    render(<DeficiencyCountCard title="Invalid Data" total={-3} />);
    expect(screen.getByText("-3")).toBeInTheDocument();
  });

  it("applies the correct class names", () => {
    render(<DeficiencyCountCard title="Check Classes" total={8} />);
    expect(screen.getByText("Check Classes")).toHaveClass(
      "deficiency-count-title"
    );
    expect(screen.getByText("8")).toHaveClass("deficiency-total-count");
  });
});
