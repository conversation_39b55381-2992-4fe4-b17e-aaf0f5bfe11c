// PieChartSkeletonLoader.test.tsx
import React from "react";
import { render, screen } from "@testing-library/react";
import PieChartSkeletonLoader from "./../../DashboardWidget/iCard/PieChartSkeletonLoader";

describe("PieChartSkeletonLoader", () => {
  it("renders without crashing", () => {
    render(<PieChartSkeletonLoader />);
    expect(screen.getByText("Due Date")).toBeInTheDocument();
    expect(screen.getByText("Severity")).toBeInTheDocument();
  });

  it("renders top loaders", () => {
    render(<PieChartSkeletonLoader />);
    const topLoaderBoxes = screen
      .getAllByRole("generic", {
        hidden: true,
      })
      .filter((el) => el.classList.contains("top-loader-box"));
    expect(topLoaderBoxes).toHaveLength(2);
  });

  it("renders pie chart placeholders", () => {
    render(<PieChartSkeletonLoader />);
    const placeholders = screen
      .getAllByRole("generic", {
        hidden: true,
      })
      .filter((el) => el.classList.contains("pie-chart-placeholder"));
    expect(placeholders).toHaveLength(2);
  });

  it("renders legends for Due Date chart", () => {
    render(<PieChartSkeletonLoader />);
    expect(screen.getByText("Overdue")).toBeInTheDocument();
    expect(screen.getByText("Due within 30 days")).toBeInTheDocument();
    expect(screen.getByText("Others")).toBeInTheDocument();
  });

  it("renders legends for Severity chart", () => {
    render(<PieChartSkeletonLoader />);
    expect(screen.getByText("High")).toBeInTheDocument();
    expect(screen.getByText("Medium")).toBeInTheDocument();
    expect(screen.getByText("Low")).toBeInTheDocument();
  });

  it("applies correct color box classes in legends", () => {
    render(<PieChartSkeletonLoader />);
    expect(document.querySelector(".color-box.overdue")).toBeInTheDocument();
    expect(document.querySelector(".color-box.due-30")).toBeInTheDocument();
    expect(document.querySelector(".color-box.others")).toBeInTheDocument();
    expect(document.querySelector(".color-box.high")).toBeInTheDocument();
    expect(document.querySelector(".color-box.medium")).toBeInTheDocument();
    expect(document.querySelector(".color-box.low")).toBeInTheDocument();
  });
});
