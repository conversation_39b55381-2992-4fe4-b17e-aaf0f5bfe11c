import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import SendEmailModal, { SendEmailPayload } from "../../SendEmailModal";
import { Option } from "react-bootstrap-typeahead/types/types";
import "@testing-library/jest-dom";

describe("SendEmailModal", () => {
  const mockOnSubmit = jest.fn().mockResolvedValue(undefined);
  const mockOnClose = jest.fn();
  const mockGenerateEmailContent = jest.fn(
    (recipients: string[]) => `Hello ${recipients.join(", ")}`
  );

  const defaultProps = {
    show: true,
    onClose: mockOnClose,
    onSubmit: mockOnSubmit,
    recipientsList: [
      { id: "1", label: "<EMAIL>" },
      { id: "2", label: "<EMAIL>" },
    ] as Option[],
    generateEmailContent: mockGenerateEmailContent,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders email modal with all required fields", () => {
    render(<SendEmailModal {...defaultProps} />);
    expect(screen.getByText("Send Email")).toBeInTheDocument();
    expect(screen.getByText("Subject *")).toBeInTheDocument();
    expect(screen.getByText("Send To *")).toBeInTheDocument();
    expect(screen.getByText("Cc")).toBeInTheDocument();
    expect(screen.getByText("Message Content *")).toBeInTheDocument();
  });

  it("calls onClose when Cancel button is clicked", () => {
    render(<SendEmailModal {...defaultProps} />);
    fireEvent.click(screen.getByText("Cancel"));
    expect(mockOnClose).toHaveBeenCalled();
  });

  it("displays validation errors for required fields", async () => {
    render(<SendEmailModal {...defaultProps} />);
    fireEvent.click(screen.getByText("Send"));
    expect(
      await screen.findByText("Please enter subject.")
    ).toBeInTheDocument();
    expect(
      await screen.findByText("Please enter message content.")
    ).toBeInTheDocument();
    expect(
      await screen.findByText("Please enter recipients.")
    ).toBeInTheDocument();
  });

  it("handles empty recipientsList (loading state)", () => {
    render(<SendEmailModal {...defaultProps} recipientsList={[]} />);

    expect(screen.getByTestId("user-list-dropdown")).toBeInTheDocument();
    expect(screen.getByTestId("cc-user-list-dropdown")).toBeInTheDocument();
  });
});
