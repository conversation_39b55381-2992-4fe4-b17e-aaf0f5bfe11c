import { renderHook } from "@testing-library/react";
import {
  useMediaQuery,
  breakpoints,
} from "../DashboardWidget/hooks/useMediaQuery";

describe("useMediaQuery", () => {
  const mockMatchMedia = (matches: boolean) => {
    window.matchMedia = jest.fn().mockImplementation((query) => ({
      matches,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      addListener: jest.fn(),
      removeListener: jest.fn(),
    }));
  };

  beforeEach(() => {
    mockMatchMedia(false);
  });

  it("should return false by default", () => {
    const { result } = renderHook(() => useMediaQuery("md"));
    expect(result.current).toBe(false);
  });

  it("should return true when media query matches", () => {
    mockMatchMedia(true);
    const { result } = renderHook(() => useMediaQuery("md"));
    expect(result.current).toBe(true);
  });

  it("should handle min-width queries", () => {
    const { result } = renderHook(() => useMediaQuery("md", "min"));
    expect(window.matchMedia).toHaveBeenCalledWith(
      `(min-width: ${breakpoints.md}px)`
    );
  });

  it("should handle max-width queries", () => {
    const { result } = renderHook(() => useMediaQuery("md", "max"));
    expect(window.matchMedia).toHaveBeenCalledWith(
      `(max-width: ${breakpoints.md - 0.02}px)`
    );
  });

  it("should cleanup listeners on unmount", () => {
    const { unmount } = renderHook(() => useMediaQuery("md"));
    unmount();
    // Cleanup is handled internally
    expect(true).toBe(true);
  });
});
