import React from "react";
import "./styles/BarChartSkeleton.scss";

export default function BarChartSkeletonLoader() {
  return (
    <div className="ra-vessel-bar-chart-wrapper">
      <div className="chart-container">
        <div className="chart-content">
          <div className="y-axis-ticks-container">
            <div className="y-axis-tick"></div>
            <div className="y-axis-tick"></div>
            <div className="y-axis-tick"></div>
            <div className="y-axis-tick"></div>
            <div className="y-axis-tick"></div>
            <div className="y-axis-tick"></div>
            <div className="y-axis-tick"></div>
            <div className="y-axis-tick"></div>
            <div className="y-axis-tick"></div>
            <div className="y-axis-tick"></div>
          </div>
          <div className="bar-rows-container">
            <div className="bar-row">
              <div className="label-loader"></div>
              <div className="bar-wrapper">
                <div className="bar-loader"></div>
              </div>
            </div>
            <div className="bar-row">
              <div className="label-loader"></div>
              <div className="bar-wrapper">
                <div className="bar-loader"></div>
              </div>
            </div>
            <div className="bar-row">
              <div className="label-loader"></div>
              <div className="bar-wrapper">
                <div className="bar-loader"></div>
              </div>
            </div>
            <div className="bar-row">
              <div className="label-loader"></div>
              <div className="bar-wrapper">
                <div className="bar-loader"></div>
              </div>
            </div>
            <div className="bar-row">
              <div className="label-loader"></div>
              <div className="bar-wrapper">
                <div className="bar-loader"></div>
              </div>
            </div>
          </div>
          <div className="x-axis-labels">
            <div className="x-axis-label">0</div>
            <div className="x-axis-label">25</div>
            <div className="x-axis-label">50</div>
            <div className="x-axis-label">75</div>
            <div className="x-axis-label">100</div>
            <div className="x-axis-label">125</div>
            <div className="x-axis-label">150</div>
            <div className="x-axis-label">175</div>
            <div className="x-axis-label">200</div>
            <div className="x-axis-label">225</div>
          </div>
        </div>
        <div className="bar-chart-legend">
          <div className="bar-chart-legend-item">
            <span className="color-box overdue"></span>Overdue
          </div>
          <div className="bar-chart-legend-item">
            <span className="color-box due-30"></span>Due within 30 Days
          </div>
          <div className="bar-chart-legend-item">
            <span className="color-box due-60"></span>Due within 60 Days
          </div>
        </div>
      </div>
    </div>
  );
}