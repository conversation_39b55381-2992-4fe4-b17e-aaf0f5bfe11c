import React from "react";
import "./styles/PieChartSkeleton.scss";

export default function PieChartSkeletonLoader() {
  return (
    <div className="dashboard-container">
      <div className="top-loaders-section">
        <div className="top-loader-box">
          <div className="loader-line-lg"></div>
          <div className="loader-line-sm"></div>
        </div>
        <div className="top-loader-box">
          <div className="loader-line-lg"></div>
          <div className="loader-line-sm"></div>
        </div>
      </div>
      <div className="pie-chart-cards-grid">
        <div className="pie-chart-card">
          <div className="pie-chart-card-header">Due Date</div>
          <div className="pie-chart-card-content">
            <div className="pie-chart-placeholder"></div>
            <div className="pie-chart-legend">
              <div className="pie-chart-legend-item"><span className="color-box overdue"></span>Overdue</div>
              <div className="pie-chart-legend-item"><span className="color-box due-30"></span>Due within 30 days</div>
              <div className="pie-chart-legend-item"><span className="color-box others"></span>Others</div>
            </div>
          </div>
        </div>
        <div className="pie-chart-card">
          <div className="pie-chart-card-header">Severity</div>
          <div className="pie-chart-card-content">
            <div className="pie-chart-placeholder"></div>
            <div className="pie-chart-legend">
              <div className="pie-chart-legend-item"><span className="color-box high"></span>High</div>
              <div className="pie-chart-legend-item"><span className="color-box medium"></span>Medium</div>
              <div className="pie-chart-legend-item"><span className="color-box low"></span>Low</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}