import React, { useState, useMemo, useCallback } from "react";
import { ChevronDown } from "lucide-react";
import classNames from "classnames";
import { CardDropdownMenu } from "./CardDropdownMenu";
import { useDropdown } from "../hooks/useDropdown";
import {
  CardDropdownProps,
  VesselGroup,
  VesselOption,
} from "../types/card-types";
import "./styles/CardDropdown.scss";

export function CardDropdown({
  groups = [],
  selectedItems,
  onSelectionChange,
  placeholder = "My Vessels",
  width,
  isSearchBoxVisible = true,
  isSelectAllVisible = true,
}: Readonly<CardDropdownProps>) {
  const { dropdownRef, isOpen, toggleDropdown } = useDropdown();
  const [searchTerm, setSearchTerm] = useState("");

  // allVessels,  is a flat array of all vessel names from all groups. It's used to manage the "Select All" functionality.
  // filteredGroups: This is the data used to render the list of vessels in the dropdown menu. It applies the search filter.
  // isAllSelected: This boolean checks if every vessel name in allVessels is also present in the selectedItems array.
  const { filteredGroups, allVessels, isAllSelected } = useMemo(() => {
    const lowerCaseSearchTerm = searchTerm.toLowerCase();
    const filtered = groups
      .map((group: VesselGroup) => ({
        ...group,
        vessels: group.vessels.filter((vessel: VesselOption) =>
          vessel.name.toLowerCase().includes(lowerCaseSearchTerm)
        ),
      }))
      .filter((group) => group.vessels.length > 0);

    const all = groups.flatMap((g: VesselGroup) =>
      g.vessels.map((v) => v.name)
    );
    const allAreSelected =
      all.length > 0 && all.every((vName) => selectedItems.includes(vName));

    return {
      filteredGroups: filtered,
      allVessels: all,
      isAllSelected: allAreSelected,
    };
  }, [groups, searchTerm, selectedItems]);

  const handleToggleVessel = useCallback(
    (vesselName: string) => {
      const newSelected = selectedItems.includes(vesselName)
        ? selectedItems.filter((v) => v !== vesselName)
        : [...selectedItems, vesselName];
      onSelectionChange(newSelected);
    },
    [selectedItems, onSelectionChange]
  );

  const handleToggleGroup = useCallback(
    (group: VesselGroup) => {
      const groupVesselNames = group.vessels.map((v) => v.name);
      const allInGroupSelected = groupVesselNames.every((vName) =>
        selectedItems.includes(vName)
      );
      const newSelected = allInGroupSelected
        ? selectedItems.filter((v) => !groupVesselNames.includes(v))
        : Array.from(new Set([...selectedItems, ...groupVesselNames]));
      onSelectionChange(newSelected);
    },
    [selectedItems, onSelectionChange]
  );

  const handleToggleAll = useCallback(() => {
    onSelectionChange(isAllSelected ? [] : allVessels);
  }, [isAllSelected, allVessels, onSelectionChange]);

  const displayText = useMemo(() => {
    if (selectedItems.length === 0) {
      return <span className="raPlaceholderText">{placeholder}</span>;
    }

    const maxVisible = width === "300px" ? 7 : 3; // for small screen count===3 should be less for bigger more
    const isOverLimit = selectedItems.length > maxVisible;

    if (isOverLimit) {
      const visibleVessels = selectedItems.slice(0, maxVisible).join(", ");
      const moreCount = selectedItems.length - maxVisible;
      const tooltipContent = selectedItems.join(", ");

      return (
        <>
          <span className="raVesselNames">{visibleVessels}</span>
          <span className="raMoreBadge" data-tooltip={tooltipContent}>
            +{moreCount} more
          </span>
        </>
      );
    }

    return <span className="raVesselNames">{selectedItems.join(", ")}</span>;
  }, [selectedItems, placeholder, width]);

  return (
    <div className={classNames("raDropdownContainer")} ref={dropdownRef}>
      <div
        className="raDropdownHeader"
        onClick={toggleDropdown}
        role="button"
        aria-label={placeholder}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
      >
        <div className="raDisplayTextContainer">{displayText}</div>
        <ChevronDown
          size={16}
          aria-label="toggle-chevron"
          className={classNames("raChevron", {
            ["raRotate"]: isOpen,
          })}
        />
      </div>

      {isOpen && (
        <CardDropdownMenu
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          filteredGroups={filteredGroups}
          selectedItems={selectedItems}
          onToggleVessel={handleToggleVessel}
          onToggleGroup={handleToggleGroup}
          isAllSelected={isAllSelected}
          onToggleAll={handleToggleAll}
          isSearchBoxVisible={isSearchBoxVisible}
          isSelectAllVisible={isSelectAllVisible}
        />
      )}
    </div>
  );
}
