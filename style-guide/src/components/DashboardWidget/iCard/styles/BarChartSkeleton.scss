$overdue-color: #e61c6b;
$due-30-color: #f7a228;
$due-60-color: #298d41;
$label-color: #4a4a4a;
$text-color: #ffffff;
$background-color: #f5f5f5;
$border-color: #e0e0e0;

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.ra-vessel-bar-chart-wrapper {
  width: 100%;
  overflow-x: auto;
}

/* Base styles for the loader UI */
.chart-container {
  width: 100%;
  max-width: 900px;
  background-color: $text-color;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.chart-content {
  position: relative;
  padding-bottom: 2rem;
}

.bar-rows-container {
  position: relative;
  z-index: 2;
  margin-top: -10px;
}

.y-axis-ticks-container {
  display: flex;
  justify-content: space-between;
  position: absolute;
  top: 0;
  bottom: 10px;
  left: 123px;
  top: -10px;
  right: 0;
  z-index: 1;
  pointer-events: none;
}

.y-axis-tick {
  height: 100%;
  width: 1px;
  background-color: $border-color;
}

.x-axis-labels {
  display: flex;
  justify-content: space-between;
  position: absolute;
  bottom: 0;
  left: 123px;
  right: 0;
  transform: translateY(2rem);
  z-index: 3;
  
  .x-axis-label {
    font-size: 12px;
    color: $label-color;
  }
}

.bar-row {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 14px;
  
  .label-loader {
    width: 100px;
    height: 22px;
    background: linear-gradient(90deg, #EDF3F7 25%, #FFFFFF 50%, #EDF3F7 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    margin-right: 1.5rem;
    border-radius: 4px;
  }

  .bar-wrapper {
    display: flex;
    flex-grow: 1;
    height: 28px;
    overflow: hidden;
    position: relative;

    .bar-loader {
      flex-grow: 1;
      background: linear-gradient(90deg, #EDF3F7 25%, #FFFFFF 50%, #EDF3F7 75%);
      background-size: 200% 100%;
      animation: skeleton-loading 1.5s infinite;
      border-radius: 4px;
    }
  }
}

.bar-chart-legend {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 3rem;
  margin-left: 6rem;
  gap: 2rem;
  flex-wrap: wrap;

  .bar-chart-legend-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: $label-color;

    .color-box {
      width: 16px;
      height: 16px;
      border-radius: 10px;
      margin-right: 8px;
    }

    .overdue {
      background-color: $overdue-color;
    }

    .due-30 {
      background-color: $due-30-color;
    }

    .due-60 {
      background-color: $due-60-color;
    }
  }
}