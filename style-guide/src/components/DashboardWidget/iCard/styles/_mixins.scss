// Import breakpoint variables
@use './variables' as *;

@mixin media($type, $breakpoint) {
  $size: null;

  @if $breakpoint == xs {
    $size: $breakpoint-xs;
  } @else if $breakpoint == sm {
    $size: $breakpoint-sm;
  } @else if $breakpoint == md {
    $size: $breakpoint-md;
  } @else if $breakpoint == lg {
    $size: $breakpoint-lg;
  } @else if $breakpoint == xl {
    $size: $breakpoint-xl;
  } @else {
    @error "Unknown breakpoint: #{$breakpoint}. Use xs, sm, md, lg, or xl.";
  }

  @if $type == 'min' {
    @media (min-width: $size) {
      @content;
    }
  } @else if $type == 'max' {
    @media (max-width: $size) {
      @content;
    }
  } @else {
    @error "Unknown media type: #{$type}. Use 'min' or 'max'.";
  }
}

@mixin media-min($breakpoint) {
  @include media('min', $breakpoint) {
    @content;
  }
}

@mixin media-max($breakpoint) {
  @include media('max', $breakpoint) {
    @content;
  }
}
